### 仅支持Linux x86,runsc模式下内核版本最低要求4.9,runlc模式下内核版本最低要求3.10


## 常用使用方式
##### 当需执行命令不存在flag/option或管道符时
```bash
sudo ./antjail  -c ./config.json ls
```
##### 当需执行的命令存在flag/option或管道符时需使用以下方式
```bash
sudo ./antjail  -c ./config.json -- bash -c "ls -ah > abc.txt" 
```

##### 容器环境使用nanovm作为加速器使用说明(宿主机无需加速器,容器环境使用加速器可带来数倍性能提升)
###### 1. 可手动查看 /sys/module 中是否存在 nanovm 目录, 如不存在则需加速器 accel 字段留空或指定为ptrace
###### 2. 在config.json中增加 system.accel = nanovm
```json
{
    "version": "0.1",
    "meta": {
        "name": "testaaa2",
        "arg": "",
        "log": "/home/<USER>/antjail/app.log"
    },
    "system": {
        "accel":"nanovm" //加速器配置
    }
}
```

#### 开启antjail调试日志 sudo ./antjail -v 4  -c ./config.json ls


## 注意事项
##### antjail最低须具备 sys_cap_admin capability
##### 目前只支持 cgroupv1 且 /sys/fs/cgroup 需不为read-only
