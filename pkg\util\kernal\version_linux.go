//go:build linux
// +build linux

package kernal

import (
	"io"
	"os"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"syscall"

	"github.com/containerd/containerd/pkg/cap"
	oci "github.com/opencontainers/runtime-spec/specs-go"
	"k8s.io/klog/v2"
)

func getKernalInfo() (KernalInfo, error) {
	var uname syscall.Utsname
	if err := syscall.Uname(&uname); err != nil {
		return KernalInfo{}, err
	}
	return KernalInfo{
		Sysname: int8ToStr(uname.Sysname[:]),
		Release: int8ToStr(uname.Release[:]),
		Version: int8ToStr(uname.Version[:]),
	}, nil
}

func KernelLower49() error {
        kInfo, err := getKernalInfo()
        if err != nil {
                return errors.New("only support linux")
        }
        klog.V(4).Infof("%#v", kInfo)
        // 将内核版本号按照"."分割成数组
        release := strings.Split(kInfo.Release, ".")
        if len(release) < 2 {
                return errors.New("get kernal version fatal")
        }
        major, _ := strconv.Atoi(release[0])
        minor, _ := strconv.Atoi(release[1])
        if major < 4 {
                return fmt.Errorf("antjail's minimal supported kernel version is 4.9,but this machine kernel version is %s", kInfo.Release)
        }
        if major == 4 && minor < 9 {
                return fmt.Errorf("antjail's minimal supported kernel version is 4.9,but this machine kernel version is %s", kInfo.Release)
        }
        return nil
}

func GetCurrentProcessCapWithoutPrivilged() (caps map[cap.Type]uint64, err error) {
	uid := os.Getuid()
	euid := os.Geteuid()
	err = syscall.Seteuid(uid)
	if err != nil {
		return
	}
	f, err := os.Open("/proc/self/status")
	if err != nil {
		return
	}
	defer f.Close()
	caps, err = cap.ParseProcPIDStatus(f)
	if err != nil {
		return
	}
	if uid != 0 {
		caps[cap.Effective] = 0
		caps[cap.Permitted] = 0
	}
	err = syscall.Seteuid(euid)
	return
}

func GetCurrentProcessCap() (capInfo oci.LinuxCapabilities, err error) {
	caps, err := GetCurrentProcessCapWithoutPrivilged()
	if err != nil {
		return
	}
	capEff := caps[cap.Effective]
	names, _ := cap.FromBitmap(capEff)
	capInfo.Effective = names

	capPer := caps[cap.Permitted]
	names, _ = cap.FromBitmap(capPer)
	capInfo.Permitted = names

	capInh := caps[cap.Inheritable]
	names, _ = cap.FromBitmap(capInh)
	capInfo.Inheritable = names

	capBound := caps[cap.Bounding]
	names, _ = cap.FromBitmap(capBound)
	capInfo.Bounding = names

	capAmbient := caps[cap.Ambient]
	names, _ = cap.FromBitmap(capAmbient)
	capInfo.Ambient = names

	return
}

func GetCurrentProcessEnv() ([]string, error) {
	maxBufferSize := 1024 * 1024
	f, err := os.Open("/proc/self/environ")
	if err != nil {
		return nil, err
	}
	defer f.Close()
	reader := io.LimitReader(f, int64(maxBufferSize))
	eData, err := io.ReadAll(reader)
	if err != nil {
		return nil, err
	}
	envs := strings.Split(string(eData), "\000")
	if len(envs) > 0 {
		envs = envs[:len(envs)-1]
	}
	return envs, nil
}
