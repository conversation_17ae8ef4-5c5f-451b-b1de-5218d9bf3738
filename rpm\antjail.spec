Name: antjail
Version:0.7
Release:14
Summary: antjail is a process-level sandbox which leverage modern light virtual machine technology


License:GPL


%description
antjail test

%prep


%build


%install
rm -rf $RPM_BUILD_ROOT
mkdir -p $RPM_BUILD_ROOT/usr/bin
mkdir -p $RPM_BUILD_ROOT/etc/antjail
cp /root/slirp4netns/slirp4netns $RPM_BUILD_ROOT/usr/bin/antslirp4netns
cp /root/antjail/antjail $RPM_BUILD_ROOT/usr/bin/
cp /root/antjail/security.json $RPM_BUILD_ROOT/etc/antjail/security_default.json

%post
chmod +s /usr/bin/antjail

%files
/usr/bin/antjail
/usr/bin/antslirp4netns
/etc/antjail/security_default.json



%changelog

