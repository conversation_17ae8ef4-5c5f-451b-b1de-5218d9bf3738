//go:build !linux
// +build !linux

package kernal

import (
	"errors"

	oci "github.com/opencontainers/runtime-spec/specs-go"
)

var NotSupportErr = errors.New("not support")

func getKernalInfo() (KernalInfo, error) {
	return KernalInfo{}, NotSupportErr
}

func GetCurrentProcessCap() (oci.LinuxCapabilities, error) {
	return oci.LinuxCapabilities{}, NotSupportErr
}


func GetCurrentProcessEnv() ([]string, error) {
	return nil, NotSupportErr
}
