package nanovm

import (
	"errors"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"syscall"

	"code.alipay.com/antjail/antjail/pkg/accel"
	"code.alipay.com/antjail/antjail/pkg/cgroup"
	"code.alipay.com/antjail/antjail/pkg/util/device"
	"code.alipay.com/antjail/antjail/pkg/util/file"
	fileUtil "code.alipay.com/antjail/antjail/pkg/util/file"
	oci "github.com/opencontainers/runtime-spec/specs-go"
	"k8s.io/klog/v2"
)

type Nanovm struct {
	deleteFileList []string
	unmount        []fileUtil.MountInfo
	accelConfig    oci.Spec
}

const (
	configDir      = "/etc/antjail/dev"
	nanovm         = "/dev/nanovm"
	supportRuntime = "runsc"
)

func init() {
	accel.Register(&Nanovm{})
}

func (n *Nanovm) Load(config *oci.Spec) error {
	n.accelConfig = *config
	if err := n.loadNanovmDevice(); err != nil {
		return err
	}
	if err := n.prepareCgroups(); err != nil {
		return err
	}
	// mkdir device mount
	// TODO: cgroup
	devicePath := filepath.Join("/sys/fs/cgroup/devices/", n.accelConfig.Linux.CgroupsPath)
	if err := os.MkdirAll(devicePath, 0666); err != nil {
		klog.Errorf("make %s dir error, detail: %s", devicePath, err.Error())
		return err
	}
	// 这里没想好，后续改掉，这种方式无法做前置统一清理工作
	n.registryFileToClear(devicePath)

	if err := device.SetSandBoxDeviceAccel(filepath.Join(devicePath, "/devices.allow"), nanovm); err != nil {
		klog.Errorf("set %s devices.allow error, detail: %s", err.Error())
		return err
	}
	return nil
}

func (*Nanovm) Type() string {
	return "nanovm"
}

// loadNanovmDevice 将nanovm放至/dev/nanovm共用，清理需站在全局视角清理,闭环暂不考虑
func (n *Nanovm) loadNanovmDevice() error {
	// check /sys/module/nanovm*
	findNanovm := false
	entries, err := os.ReadDir("/sys/module")
	if err != nil {
		return errors.New("the accel 'nanovm' is not supported in this machine, please try another accel such as 'ptrace'")
	}
	for _, v := range entries {
		if strings.HasPrefix(v.Name(), "nanovm") {
			findNanovm = true
			break
		}
	}
	if !findNanovm {
		return errors.New("the accel 'nanovm' is not supported in this machine, please try another accel such as 'ptrace'")
	}
	// 如果 /dev/nanovm已存在则跳过
	if _, err := os.Stat(nanovm); err == nil {
		if err := device.SetSandBoxDeviceAccel("/sys/fs/cgroup/devices/devices.allow", nanovm); err != nil {
			klog.Errorf("set %s devices.allow error, detail: %s", err.Error())
			return err
		}
		return nil
	} else if err != nil && !os.IsNotExist(err) {
		klog.V(4).Infof("get nanovm stat error, detai: %s, mount again", err.Error())
	}

	if err := os.MkdirAll(configDir, 0666); err != nil {
		klog.Errorf("mkdir %s error, detail: %s", configDir, err.Error())
		return err
	}

	// 此处挂载是因为在容器 /dev/ 下nanovm默认是不可见的，需要使用cap_sys_admin权限将其挂载到另一个目录nanovm才会出现
	mountInfo := file.MountInfo{
		Source: "dev",
		Target: configDir,
		FSType: "devtmpfs",
	}
	if err := file.Mount(mountInfo); err != nil {
		klog.Errorf("mount devtmpfs to %s error, detail: %s", configDir, err.Error())
		return err
	}

	nanovmPath, err := os.Readlink(filepath.Join(configDir, "nanovm"))
	if err != nil {
		if pe, ok := err.(*os.PathError); ok {
			if os.IsNotExist(pe.Unwrap()) {
				klog.Errorf("not found accel nanovm in /dev")
			}
		}
		klog.Errorf("get nanovm symlink error, detail: %s", err.Error())
		return err
	}
	nanovmPath = filepath.Join(configDir, filepath.Base(nanovmPath))
	if err := os.Symlink(nanovmPath, "/dev/nanovm"); err != nil {
		klog.Errorf("link nanovm failed, detail: %s", err.Error())
		return err
	}
	if err := device.SetSandBoxDeviceAccel("/sys/fs/cgroup/devices/devices.allow", nanovm); err != nil {
		klog.Errorf("set %s devices.allow error, detail: %s", err.Error())
		return err
	}
	return nil
}

// registryFileToClear 注册unload时需清理的文件或目录,此方式繁琐,后续看是否有其他办法
func (n *Nanovm) registryFileToClear(file ...string) {
	n.deleteFileList = append(n.deleteFileList, file...)
}

func (n *Nanovm) Unload() {
	for _, v := range n.deleteFileList {
		if err := syscall.Rmdir(v); err != nil {
			klog.V(4).Infof("delete dir %s error, detail: %s", v, err.Error())
		}
	}
}

// TODO: cgroup
// prepareCgroups ...
func (n *Nanovm) prepareCgroups() error {
	sandboxCgroupPath := filepath.Join(cgroup.CgroupV1Root, "cpuset", n.accelConfig.Linux.CgroupsPath)
	if err := os.MkdirAll(sandboxCgroupPath, 0666); err != nil {
		klog.Errorf("create %s error, detail:%s", sandboxCgroupPath, err.Error())
		return err
	}
	n.registryFileToClear(sandboxCgroupPath)

	bcpus, _ := os.ReadFile("/sys/fs/cgroup/cpuset/cpuset.cpus")
	err := os.WriteFile(filepath.Join(sandboxCgroupPath, "cpuset.cpus"), bcpus, 0666)
	if err != nil {
		klog.Errorf("write nanovm cpuset.cpus error, detail: %s", err.Error())
		return err
	}

	bmems, _ := os.ReadFile("/sys/fs/cgroup/cpuset/cpuset.mems")
	if err := os.WriteFile(filepath.Join(sandboxCgroupPath, "cpuset.mems"), bmems, 0666); err != nil {
		klog.Errorf("write nanovm cpuset.mems error, detail: %s", err.Error())
		return err
	}
	// set antjail user nanovm
	f, err := os.OpenFile("/sys/fs/cgroup/devices/cgroup.procs", os.O_APPEND|os.O_RDWR|os.O_CREATE, 0666)
	if err != nil {
		klog.Errorf("open device cgroup error, detail: %s", err.Error())
		return err
	}
	defer f.Close()
	pid := os.Getpid()
	if _, err := f.Write([]byte(strconv.Itoa(pid))); err != nil {
		klog.Errorf("make antjail use nanovm error, detail: %s", err.Error())
		return err
	}

	return nil
}
