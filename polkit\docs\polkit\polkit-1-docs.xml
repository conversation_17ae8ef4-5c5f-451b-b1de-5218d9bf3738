<?xml version="1.0"?>
<!DOCTYPE book PUBLIC "-//OASIS//DTD DocBook XML V4.1.2//EN"
               "http://www.oasis-open.org/docbook/xml/4.1.2/docbookx.dtd" [
<!ENTITY version SYSTEM "../version.xml">
<!ENTITY extensiondir SYSTEM "../extensiondir.xml">
]>
<book id="index" xmlns:xi="http://www.w3.org/2003/XInclude">
  <bookinfo>
    <title>polkit Reference Manual</title>
    <releaseinfo>
      For version &version; — the latest version of this
      documentation can be found at <ulink role="online-location"
      url="http://www.freedesktop.org/software/polkit/docs/latest/">http://www.freedesktop.org/software/polkit/docs/latest/</ulink>.
    </releaseinfo>
  </bookinfo>

  <xi:include href="overview.xml"/>

  <part id="ref-dbus-api">
    <title>D-Bus API Reference</title>
    <xi:include href="docbook-interface-org.freedesktop.PolicyKit1.Authority.xml"/>
    <xi:include href="docbook-interface-org.freedesktop.PolicyKit1.AuthenticationAgent.xml"/>
  </part>

  <part id="ref-api">
    <title>Library API Reference</title>
    <xi:include href="xml/polkitauthority.xml"/>
    <xi:include href="xml/polkitauthorizationresult.xml"/>
    <xi:include href="xml/polkitdetails.xml"/>
    <xi:include href="xml/polkiterror.xml"/>
    <xi:include href="xml/polkitactiondescription.xml"/>
    <xi:include href="xml/polkittemporaryauthorization.xml"/>
    <xi:include href="xml/polkitpermission.xml"/>
    <chapter id="subjects">
      <title>Subjects</title>
      <xi:include href="xml/polkitsubject.xml"/>
      <xi:include href="xml/polkitunixprocess.xml"/>
      <xi:include href="xml/polkitunixsession.xml"/>
      <xi:include href="xml/polkitsystembusname.xml"/>
    </chapter>
    <chapter id="Identities">
      <title>Identities</title>
      <xi:include href="xml/polkitidentity.xml"/>
      <xi:include href="xml/polkitunixuser.xml"/>
      <xi:include href="xml/polkitunixgroup.xml"/>
      <xi:include href="xml/polkitunixnetgroup.xml"/>
    </chapter>
  </part>

  <part id="ref-authentication-agent-api">
    <title>Authentication Agent API Reference</title>
    <xi:include href="xml/polkitagentlistener.xml"/>
    <xi:include href="xml/polkitagenttextlistener.xml"/>
    <xi:include href="xml/polkitagentsession.xml"/>
  </part>

  <part id="manpages">
    <title>Manual Pages</title>
    <xi:include href="../man/polkit.xml"/>
    <xi:include href="../man/polkitd.xml"/>
    <xi:include href="../man/pkcheck.xml"/>
    <xi:include href="../man/pkaction.xml"/>
    <xi:include href="../man/pkexec.xml"/>
    <xi:include href="../man/pkttyagent.xml"/>
  </part>

  <chapter id="polkit-hierarchy">
    <title>Object Hierarchy</title>
      <xi:include href="xml/tree_index.sgml"/>
  </chapter>

  <xi:include href="xml/annotation-glossary.xml"><xi:fallback /></xi:include>

  <index id="polit-index">
    <title>Index</title>
  </index>

  <appendix id="license">
    <title>License</title>
    <para>
<programlisting><xi:include xmlns:xi="http://www.w3.org/2001/XInclude" href="../../COPYING" parse="text"><xi:fallback>FIXME: MISSING XINCLUDE CONTENT</xi:fallback></xi:include></programlisting>
    </para>
  </appendix>
</book>
