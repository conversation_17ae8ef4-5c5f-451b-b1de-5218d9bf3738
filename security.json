{"version": "0.1", "meta": {"name": "", "arg": ""}, "system": {"runtime": "runlc", "accel": "systrap", "rollback": "runlc", "rollbackOverWrite": {"security.network.mode": "host"}, "root": {"path": "/", "paths": [], "readonly": true}, "memory": {"max": 1073741824}, "cpu": {"number": 4}, "cwd": "/home/<USER>/antjail/antjail"}, "security": {"network": {"mode": "host", "listen": [], "tcp": [], "udp": [], "dns": []}, "file": {"writablePaths": [], "writableNoexecPaths": [], "readonlyPaths": [], "maskedPaths": []}, "execve": {"mode": "whitelist", "paths": []}, "cap": {"add": [], "drop": []}, "seccomp": {"mode": "block", "syscallList": [{"names": ["unshare", "fork"]}, {"names": ["clone"], "args": [{"index": 1, "op": "SCMP_CMP_MASKED_EQ", "value": 256, "valueTwo": 0}]}]}}}