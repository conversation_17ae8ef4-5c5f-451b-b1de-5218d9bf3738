package librunlc

import (
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"syscall"

	"code.alipay.com/antjail/antjail/pkg/runtime"
	"k8s.io/klog/v2"
)

func applyDNS(name string) func() {
	// make resolve file
	tempDir := os.TempDir()

	trFile := filepath.Join(tempDir, name)
	f, err := os.OpenFile(trFile, os.O_CREATE|os.O_TRUNC|os.O_RDWR, fs.ModePerm)
	if err != nil {
		klog.Fatalf("create resolve file %s error: %s", trFile, err.Error())
	}
	// default dns
	if _, err := f.WriteString("nameserver ********"); err != nil {
		klog.Fatalf("write namespace to %s error: %s", trFile, err.Error())
	}
	defer f.Close()
	resolveConf, err := filepath.EvalSymlinks("/etc/resolv.conf")
	if err != nil {
		klog.Fatal("get /etc/resolv.conf error: %s", err.Error())
	}
	if err := syscall.Mount(trFile, resolveConf, "none", syscall.MS_BIND|syscall.MS_REC, ""); err != nil {
		klog.Fatalf("mount %s to  /etc/resolv.conf error: %s", trFile, err.Error())
	}
	if err := os.Chmod("/etc/resolv.conf", 0644); err != nil {
		klog.Fatalf("chmod /etc/resolve.conf error: %s", err.Error())
	}
	return func() {
		syscall.Unmount(resolveConf, 0)
		os.Remove(trFile)
	}
}

func applyNetwork(tid int, config *runtime.Config) func() error {
	tcpPolicy := ""
	domainPolicy := ""
	udpPolicy := ""
	if len(config.SecurityConfig.Security.Network.Policy.TCP) != 0 {
		tcpPolicy = strings.Join(config.SecurityConfig.Security.Network.Policy.TCP, ",")
	}
	if len(config.SecurityConfig.Security.Network.Policy.UDP) != 0 {
		udpPolicy = strings.Join(config.SecurityConfig.Security.Network.Policy.UDP, ",")
	}
	if len(config.SecurityConfig.Security.Network.Policy.DNS) != 0 {
		domainPolicy = strings.Join(config.SecurityConfig.Security.Network.Policy.DNS, ",")
	}
	enableDebug := false
	if klog.V(4).Enabled() {
		enableDebug = true
	}

	driver, err := NewParentDriver(&WrapKlog{}, 0, nil, "tap0", true, "", false, false, false, tcpPolicy, udpPolicy, domainPolicy, enableDebug)
	if err != nil {
		klog.Fatalf("get slirp4netns driver error: %s", err.Error())
	}

	_, cleanFn, err := driver.ConfigureNetwork(tid, "tap0")
	if err != nil {
		klog.Fatalf("configureNetwork error: %s", err.Error())
	}

	return cleanFn
}

type WrapKlog struct{}

func (*WrapKlog) Write(p []byte) (int, error) {
	klog.Info(string(p))
	return len(p), nil
}
