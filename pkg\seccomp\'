package seccomp

import (
	"fmt"
	"encoding/json"

	"github.com/docker/docker/profiles/seccomp"

	oci "github.com/opencontainers/runtime-spec/specs-go"
)




func SetBlockSeccompConfig(blockSyscallList []string, config *oci.Spec) error {
	blockTemplate := seccomp.Seccomp{
		LinuxSeccomp: oci.LinuxSeccomp{
			DefaultAction: oci.ActAllow,
		},
		Syscalls: []*seccomp.Syscall{
			&seccomp.Syscall{
				LinuxSyscall: oci.LinuxSyscall{
					Names: blockSyscallList,
					Action: oci.ActErrno,
				},
			},
		},
	}

	configD, err := json.Marshal(blockTemplate)
	if err != nil {
		return err
	}

	blockSeccomp, err := seccomp.LoadProfile(string(configD), config)
	if err != nil {
		return err
	}
	config.Linux.Seccomp = blockSeccomp
	return nil
}
