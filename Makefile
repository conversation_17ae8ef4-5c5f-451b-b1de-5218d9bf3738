# Antennae

COMMIT ?= $(shell git rev-parse --short HEAD)
RELEASE ?= "0.1.0"
REPO_URL?= "https://code.alipay.com/antjail/antjail"
IMPORTROOT := "code.alipay.com/antjail/antjail"

LD_FLAGS := "-X ${IMPORTROOT}/cmd.COMMIT=${COMMIT} -X ${IMPORTROOT}/cmd.REPO=${REPO_URL} -X ${IMPORTROOT}/cmd.RELEASE=${RELEASE} -extldflags '-static'"
BUILD_TAGS := "seccomp"

antjail:
	CGO_ENABLED=1 go build -o antjail -a  -ldflags=${LD_FLAGS} --tags=${BUILD_TAGS}  main.go
	chmod u+s ./antjail

antjail_arm64:
	CGO_ENABLED=1 GOARCH=arm64  go build -o antjail -a  -ldflags=${LD_FLAGS} --tags=${BUILD_TAGS}  main.go
	chmod u+s ./antjail

.PHONY: antjail antjail_arm64

tar: antjail security.json
	cp -f antjail deploy/
	cp -f security.json deploy/
	rm deploy/antjail_${RELEASE}*
	tar zcvf deploy/antjail_${RELEASE}_${COMMIT}.tar.gz -Cdeploy antjail security.json install.sh

rpm: antjail security.json
	rpmdev-setuptree
	cp rpm/antjail.spec /root/rpmbuild/SPECS/
	rpmbuild  -bb /root//rpmbuild/SPECS/antjail.spec
