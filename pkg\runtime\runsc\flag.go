package runsc

import (
	"encoding/json"
	"errors"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"syscall"

	"code.alipay.com/antjail/antjail/asset"
	"code.alipay.com/antjail/antjail/pkg/cgroup"
	configPkg "code.alipay.com/antjail/antjail/pkg/config"
	"code.alipay.com/antjail/antjail/pkg/runtime"
	fileUtil "code.alipay.com/antjail/antjail/pkg/util/file"

	"k8s.io/klog/v2"
)



func prepareRootfs(bundle string, config *runtime.Config) {
	paths := config.SecurityConfig.System.Root.Paths
	rootfsPath := filepath.Join(bundle, "rootfs")
        if err := os.Mkdir(rootfsPath, fs.ModePerm); err != nil && !errors.Is(err, os.ErrExist) {
                klog.Fatalf("create run dir %s error, detail: %s", rootfsPath, err.Error())
		return
        }

	if len(paths) == 0 {
		klog.V(4).Info("preapre Rootfs from path ...")
		if err := syscall.Mount(config.SecurityConfig.System.Root.Path, rootfsPath, "none", syscall.MS_BIND | syscall.MS_REC, ""); err != nil {
			klog.Fatalf("mount root path %s to %s error", config.SecurityConfig.System.Root.Path, rootfsPath)
			return
		}
		return
	}
	klog.V(4).Info("preapre Rootfs from paths ...")

	for _, path := range paths {
		fileInfo, err := os.Lstat(path)
		if err != nil {
			klog.Fatalf("Error retrieving file information:%s", err.Error())
			return
		}
		mode := fileInfo.Mode()
		newPath := rootfsPath + "/" + path
		switch {
                case mode.IsRegular():
                        dirPath := filepath.Dir(newPath)
                        if _, err := os.Stat(dirPath); os.IsNotExist(err) {
                                os.MkdirAll(dirPath, fs.ModePerm)
                        }
                        file, err := os.Create(newPath)
                        if err != nil {
                                klog.Fatalf("Error creating file: %s", err.Error())
                                return
                        }
                        defer file.Close()
                        if err := syscall.Mount(path, newPath, "none", syscall.MS_BIND|syscall.MS_REC, ""); err != nil {
                                klog.Fatalf("mount %s to  %s error: %s", path, newPath, err.Error())
                        }
                case mode.IsDir():
                        if err := os.MkdirAll(newPath, fs.ModePerm); err != nil && !errors.Is(err, os.ErrExist) {
                                klog.Fatalf("create run dir %s error, detail: %s", newPath, err.Error())
                        }
                        if err := syscall.Mount(path, newPath, "none", syscall.MS_BIND|syscall.MS_REC, ""); err != nil {
                                klog.Fatalf("mount %s to  %s error: %s", path, newPath, err.Error())
                        }
                        if err := syscall.Mount(newPath, newPath, "none", syscall.MS_BIND | syscall.MS_REC | syscall.MS_REMOUNT, ""); err != nil {
                                klog.Fatal("mount %s to readonly error: %s", newPath, err.Error())
                        }

                case mode&os.ModeSymlink != 0:
                        resolvedPath, err := os.Readlink(path)
                        if err != nil {
                                klog.Fatalf("readlink %s error: %s", path, err.Error())
                        }
                        if err := os.Symlink(resolvedPath, newPath); err != nil {
                                klog.Fatal("create link file % error: %s", path, err.Error())
                        }
                default:
                        dirPath := filepath.Dir(newPath)
                        if _, err := os.Stat(dirPath); os.IsNotExist(err) {
                                os.MkdirAll(dirPath, fs.ModePerm)
                        }

                        file, err := os.Create(newPath)
                        if err != nil {
                                klog.Fatalf("Error creating file: %s", err.Error())
                                return
                        }
                        defer file.Close()
                        if err := syscall.Mount(path, newPath, "none", syscall.MS_BIND|syscall.MS_REC, ""); err != nil {
                                klog.Fatalf("mount %s to  %s error: %s", path, newPath, err.Error())
                        }
		/*
		case mode.IsRegular():
			file, err := os.Create(newPath)
			if err != nil {
				klog.Fatalf("Error creating file: %s", err.Error())
				return
			}
			defer file.Close()
			if err := syscall.Mount(path, newPath, "none", syscall.MS_BIND|syscall.MS_REC, ""); err != nil {
				klog.Fatalf("mount %s to  %s error: %s", path, newPath, err.Error())
			}
		case mode.IsDir():
			if err := os.Mkdir(newPath, fs.ModePerm); err != nil && !errors.Is(err, os.ErrExist) {
				klog.Fatalf("create run dir %s error, detail: %s", newPath, err.Error())
			}
                        if err := syscall.Mount(path, newPath, "none", syscall.MS_BIND|syscall.MS_REC | syscall.MS_PRIVATE, ""); err != nil {
                                klog.Fatalf("mount %s to  %s error: %s", path, newPath, err.Error())
                        }
			klog.V(4).Infof("Mount %s successful", path)

		case mode&os.ModeSymlink != 0:
			resolvedPath, err := os.Readlink(path)
			if err != nil {
				klog.Fatalf("readlink %s error: %s", path, err.Error())
			}
			if err := os.Symlink(resolvedPath, newPath); err != nil {
				klog.Fatal("create link file % error: %s", path, err.Error())
			}
		default:
			klog.Fatalf("% is of a different type", path)*/
		}
	}
}



func (r *runsc) prepareRunsc(flags []string, config *runtime.Config) []string {
	r.runscfile = filepath.Join(runscBinaryDir, gvisorRunscfile)
	if config.SecurityConfig.System.Accel == "systrap" {
		r.runscfile = filepath.Join(runscBinaryDir, gvisorRunscfile)
	}

	args := make([]string, 0, 1)
	if config.SecurityConfig.Meta.Arg != "" {
		args = strings.Split(config.SecurityConfig.Meta.Arg, " ")
	}

	// rootless 时使用gvisor
	if runtime.Rootless {
		r.runscfile = filepath.Join(runscBinaryDir, gvisorRunscfile)
		addRootlessArg := true
		addRootDirArg := true
		for _, v := range args {
			if v == "-rootless" {
				addRootlessArg = false
			}
			if v == "-root" {
				addRootDirArg = false
			}
		}

		if addRootlessArg {
			args = append(args, "--rootless")
		}

		if addRootDirArg {
			args = append(args, "--root="+filepath.Join(runtime.Srootdir, ".gvisor_containers"))
		}

		// change spec uid gid as 0
		config.OCIConfig.Process.User.GID = 0
		config.OCIConfig.Process.User.UID = 0
	}

	if config.OCIConfig.Linux.Seccomp != nil {
		addOCISeccomp := true
		for _, v := range args {
			if v == "--oci-seccomp" {
				addOCISeccomp = false
				break
			}
		}
		if addOCISeccomp {
			args = append(args, "--oci-seccomp")
		}
	}

	config.SecurityConfig.Meta.Arg = strings.Join(args, " ")

	if len(flags) == 0 {
		klog.Fatal("not found user command")
	}
	config.OCIConfig.Process.Args = flags
	/* wd, err := os.Getwd()
	if err != nil {
		klog.Fatal("get process work directory error, detail: %s", err.Error())
	}*/
	config.OCIConfig.Process.Cwd = config.SecurityConfig.System.Cwd

	klog.V(4).Infof("release runsc to %s", runscBinaryDir)
	if err := os.MkdirAll(runscBinaryDir, 0773); err != nil && !errors.Is(err, os.ErrExist) {
		klog.Fatalf("create %s error, detail: %s", runscBinaryDir, err.Error())
	}
	if _, err := os.Stat(r.runscfile); err != nil && errors.Is(err, os.ErrNotExist) {
		// get runsc file
		runscBinary, err := asset.RunscBinary.ReadFile(filepath.Base(r.runscfile))
		if err != nil {
			klog.Fatalf("not found %s", r.runscfile)
		}
		fileUtil.MustWriteByteToFile(r.runscfile, runscBinary)
	} else if err != nil {
		klog.Fatalf("stat % error, detail: %s", r.runscfile, err.Error())
	}

	securityConfig := config.SecurityConfig

	sandboxName := securityConfig.Meta.Name

        if err := syscall.Unshare(syscall.CLONE_NEWNS); err != nil {
                klog.Fatalf("unshare new mount namespace error, detail: %s", err.Error())
        }


        syscall.Mount("none", "/", "", syscall.MS_REC|syscall.MS_SLAVE, "")

	klog.V(4).Infof("start create file run dir")
	fileUtil.CreateRunDir(runtime.Srootdir, sandboxName)
	klog.V(4).Infof("end create file run dir")


	prepareRootfs(filepath.Join(runtime.Srootdir, sandboxName), config)
	config.OCIConfig.Root.Path=filepath.Join(runtime.Srootdir, sandboxName, "rootfs")
	runscFlags := make([]string, 0, 2)
	if securityConfig.Meta.Arg != "" {
		runscFlags = append(runscFlags, strings.Split(securityConfig.Meta.Arg, " ")...)
	}

	switch securityConfig.Security.Network.Mode {
	case "none":
		runscFlags = append(runscFlags, "--network", "none")
	case "host":
		runscFlags = append(runscFlags, "--network", "host")
	case "hostwithpolicy":
		runscFlags = append(runscFlags, "--network", "host")
	}
	aclFile := filepath.Join(runtime.Srootdir, sandboxName, "netacl.json")
	d, err := json.Marshal(securityConfig.Security)
	if err != nil {
		klog.Fatalf("marshal %s to json error, detail: %s", securityConfig.Security, err.Error())
	}
	runscFlags = append(runscFlags, "--acl", aclFile)
	klog.V(4).Info("start write network policy to file")
	fileUtil.MustWriteContentToFile(aclFile, string(d))

	klog.V(4).Info("write network policy to file end")

	ociConfig, err := json.Marshal(configPkg.GetSecurityOCIConfig())
	if err != nil {
		klog.Fatalf("marshal oci config error, detail: %s", err.Error())
	}

	klog.V(4).Infof("write oci config to file %s", filepath.Join(runtime.Srootdir, sandboxName, "config.json"))
	klog.V(4).Info(string(ociConfig))
	fileUtil.MustWriteContentToFile(filepath.Join(runtime.Srootdir, sandboxName, "config.json"), string(ociConfig))

	return runscFlags
}

// deleteCgroup mem 和 cpu 交给runsc配置，自行清理下,来保证异常退出时不会残留文件影响下次
func deleteCgroup(sandboxName string) {
	cp := filepath.Join(cgroup.CgroupV1Root, "cpu", sandboxName)
	mp := filepath.Join(cgroup.CgroupV1Root, "memory", sandboxName)
	fileUtil.MustRemoveDir(cp)
	fileUtil.MustRemoveDir(mp)
}
