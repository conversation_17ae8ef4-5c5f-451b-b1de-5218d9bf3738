package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"syscall"


	util "code.alipay.com/antjail/antjail/pkg/util/kernal"

	"github.com/docker/docker/oci/caps"
	"github.com/docker/docker/profiles/seccomp"
	"github.com/google/uuid"
	oci "github.com/opencontainers/runtime-spec/specs-go"
)

type MergeSecurityConfigWithTemplateFunc func(*SecurityConfig)

var mergeFuncs []MergeSecurityConfigWithTemplateFunc

func RegistryMergeFunc(f MergeSecurityConfigWithTemplateFunc) {
	mergeFuncs = append(mergeFuncs, f)
}

func MergeSecurityConfigWithTemplate(securityConfig *SecurityConfig) *oci.Spec {
	for _, mf := range mergeFuncs {
		mf(securityConfig)
	}
	return securityTemplateConfig
}

func init() {
	RegistryMergeFunc(mergeSystem)
	RegistryMergeFunc(mergeSecurity)
	RegistryMergeFunc(mergeCommandLine)
	RegistryMergeFunc(mergeMount)
	RegistryMergeFunc(mergeUser)
	RegistryMergeFunc(mergeEnv)
	RegistryMergeFunc(mergeCap)
	RegistryMergeFunc(mergeSeccomp)
}

func mergeSystem(securityConfig *SecurityConfig) {
	if securityConfig.System.Root.Path != "" {
		securityTemplateConfig.Root.Path = securityConfig.System.Root.Path
	}
	if securityConfig.System.Root.Readonly != nil {
		securityTemplateConfig.Root.Readonly = *securityConfig.System.Root.Readonly
	}
	if securityConfig.System.CPU.Number != 0 {
		cpuQuota := 100000 * securityConfig.System.CPU.Number
		securityTemplateConfig.Linux.Resources.CPU.Quota = &cpuQuota
	}
	if securityConfig.System.Memory.Max != 0 {
		memoryQuota := securityConfig.System.Memory.Max
		securityTemplateConfig.Linux.Resources.Memory.Limit = &memoryQuota
	}
	if len(securityConfig.Meta.Name) == 0 {
		securityConfig.Meta.Name = uuid.New().String()
	}
	if securityTemplateConfig.Linux.CgroupsPath == "" {
		securityTemplateConfig.Linux.CgroupsPath = securityConfig.Meta.Name
	} else if strings.Contains(securityTemplateConfig.Linux.CgroupsPath, "/") {
		panic("not support cgroupsPath with /")
	}
	securityTemplateConfig.Linux.CgroupsPath = "antjail-" + securityTemplateConfig.Linux.CgroupsPath
}

func mergeSecurity(securityConfig *SecurityConfig) {
	namespaces := securityTemplateConfig.Linux.Namespaces
	rns := make([]oci.LinuxNamespace, 0, len(namespaces))
	for i, v := range namespaces {
		if v.Type == oci.NetworkNamespace {
			continue
		}
		rns = append(rns, namespaces[i])

	}
	securityTemplateConfig.Linux.Namespaces = rns
}

func mergeCommandLine(securityConfig *SecurityConfig) {
	securityTemplateConfig.Process.Args = securityConfig.Entrypoint
	d, err := os.Getwd()
	if err != nil {
		panic(fmt.Sprintf("get process cwd error, detail: %s", err.Error()))
	}
	securityTemplateConfig.Process.Cwd = d
}

func mergeMount(securityConfig *SecurityConfig) {
	if len(securityConfig.Security.File.WritablePaths) > 0 {
		for _, m := range securityConfig.Security.File.WritablePaths {
			securityTemplateConfig.Mounts = append(securityTemplateConfig.Mounts, oci.Mount{
				Destination: m,
				Type:        "bind",
				Source:      m,
				Options: []string{
					"rbind",
					"rw",
				},
			})
		}
	}
	if len(securityConfig.Security.File.WritableNoexecPaths) > 0 {
		for _, m := range securityConfig.Security.File.WritableNoexecPaths {
                        securityTemplateConfig.Mounts = append(securityTemplateConfig.Mounts, oci.Mount{
                                Destination: m,
                                Type:        "bind",
                                Source:      m,
                                Options: []string{
                                        "rbind",
                                        "rw",
					"noexec",
                                },
                        })
		}
	}
	if len(securityConfig.Security.File.MaskedPaths) > 0 {
		securityTemplateConfig.Linux.MaskedPaths = append(securityTemplateConfig.Linux.MaskedPaths, securityConfig.Security.File.MaskedPaths...)
	}
	if len(securityConfig.Security.File.ReadonlyPaths) > 0 {
		securityTemplateConfig.Linux.ReadonlyPaths = append(securityTemplateConfig.Linux.ReadonlyPaths, securityConfig.Security.File.ReadonlyPaths...)
	}
}

func mergeUser(SecurityConfig *SecurityConfig) {
	gid := os.Getgid()
	uid := os.Getuid()
	uidUint32 := uint32(uid)
	gidUint32 := uint32(gid)
	if strconv.Itoa(uid) != strconv.FormatUint(uint64(uidUint32), 10) {
		panic("uid is invalid")
	}

	if strconv.Itoa(gid) != strconv.FormatUint(uint64(gidUint32), 10) {
		panic("gid is invalid")
	}

	securityTemplateConfig.Process.User.UID = uidUint32
	securityTemplateConfig.Process.User.GID = gidUint32
	umaskValue := 022
	oldmask := syscall.Umask(umaskValue)
	syscall.Umask(oldmask)
	oldmaskUint32 := uint32(oldmask)
	if strconv.Itoa(oldmask) != strconv.FormatUint(uint64(oldmask), 10) {
		panic("umask is invalid")
	}
	securityTemplateConfig.Process.User.Umask = &oldmaskUint32
}

func mergeCap(securityConfig *SecurityConfig) {
	capabilities, err := caps.TweakCapabilities(
		DefaultCapabilities(),
		securityConfig.Security.Cap.Add,
		securityConfig.Security.Cap.Drop,
		false,
	)
	if err != nil {
		panic(fmt.Sprintf("tweak cap error: %s", err.Error()))
	}

	// TODO: 当支持指定process.user.id时需考虑只赋值给 bounding
	securityTemplateConfig.Process.Capabilities = &oci.LinuxCapabilities{
		Effective: capabilities,
		Bounding:  capabilities,
		Permitted: capabilities,
	}
}

func mergeSeccomp(securityConfig *SecurityConfig) {
	var err error
	switch profile := securityConfig.Security.Seccomp.Mode; profile {
	case "unconfined":
		return
	case "", "builtin":
		securityTemplateConfig.Linux.Seccomp, err = seccomp.GetDefaultProfile(securityTemplateConfig)
		if err != nil {
			panic(fmt.Sprintf("load defalt seccomp error: %s", err.Error()))
		}
	case "block":
		if err := setBlockSeccompConfig(securityConfig.Security.Seccomp.SyscallList, securityTemplateConfig); err != nil {
			panic(fmt.Sprintf("set seccomp to spec error: %s", err.Error()))
		}
	default:
		//securityTemplateConfig.Linux.Seccomp, err = seccomp.LoadProfile(securityConfig.Security.Seccomp, securityTemplateConfig)
		//if err != nil {
		//panic(fmt.Sprintf("load seccomp from body: %s error: %s", securityConfig.Security.Seccomp, err.Error()))
		panic(fmt.Sprintf("seccomp not support mode %s", profile))
		}
}

func mergeEnv(securityConfig *SecurityConfig) {
	// get current process env
	envs, err := util.GetCurrentProcessEnv()
	if err != nil {
		panic("get current process env panic")
	}
	securityTemplateConfig.Process.Env = envs
}

