//go:build !arm64
// +build !arm64

package asset

import (
	"embed"
	"io"
	"path/filepath"
)

//go:embed gvisor_runsc_amd64 nanovisor_runsc_amd64 security.json
var static embed.FS

func (fs *RunscBinaryFS) ReadFile(filename string) ([]byte, error) {
	if filepath.Ext(filename) == "" {
		filename += "_amd64"
	}
	file, err := fs.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()
	return io.ReadAll(file)
}
