prefix=@prefix@
exec_prefix=@exec_prefix@
libdir=@libdir@
includedir=@includedir@
policydir=@datarootdir@/polkit-1/actions/
actiondir=@datarootdir@/polkit-1/actions/

Name: polkit-gobject-1
Description: PolicyKit Authorization API
Version: @VERSION@
Libs: -L${libdir} -lpolkit-gobject-1
Cflags: -I${includedir}/polkit-1
Requires: gio-2.0 >= 2.18 glib-2.0 >= 2.18
# Programs using pkcheck can use this to determine
# whether or not it can be passed a uid.
pkcheck_supports_uid=true
