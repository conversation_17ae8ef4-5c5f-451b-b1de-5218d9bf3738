package util

import (
	"context"
	"os"
	"os/exec"
	"time"

	"k8s.io/klog/v2"
)

type Command struct {
	*exec.Cmd
	sendSignal func(os.Signal) error
	ctx        context.Context
}

func CommandContextWithSignal(ctx context.Context, name string, sendSignalFunc func(os.Signal) error, arg ...string) *Command {
	cmd := exec.CommandContext(ctx, name, arg...)
	return &Command{
		Cmd:        cmd,
		ctx:        ctx,
		sendSignal: sendSignalFunc,
	}
}

func (cmd *Command) Start() error {
	if err := cmd.Cmd.Start(); err != nil {
		klog.Errorf("start user command %s error: %s", cmd.Cmd.String(), err.Error())
		return err
	}
	return nil
}

func (cmd *Command) Wait(daemon bool) error {
        waitChan := make(chan error, 1)
        osSignal := cmd.ctx.Value("signal").(chan os.Signal)
        cancelFunc := cmd.ctx.Value("cancelFunc").(context.CancelFunc)
        go func() {
                select {
                case signal := <-osSignal:
                        if cmd.sendSignal == nil {
                                cmd.sendSignal = cmd.Cmd.Process.Signal
                        }
                        if err := cmd.sendSignal(signal); err != nil {
                                klog.Errorf("send signal to sandbox error: %s", err.Error())
                                close(waitChan)
                        }
                case <-waitChan:
                        return
                }
                timer := time.NewTicker(30 * time.Second)
                select {
                case <-timer.C:
                        cancelFunc()
                case <-waitChan:
                }
                timer.Stop()
        }()

        if !daemon {
                if err := cmd.Cmd.Wait(); err != nil {
                        return err
                }
        }
        close(waitChan)
        return nil
}
// Run ...
func (cmd *Command) Run(daemon bool) error {
	/*if err := cmd.Cmd.Start(); err != nil {
		klog.Errorf("start user command %s error: %s", cmd.Cmd.String(), err.Error())
		return err
	}*/
	if err := cmd.Start(); err != nil {
		klog.Errorf("cmd.Start() error:%s", err.Error())
		return err
	}
	if err := cmd.Wait(daemon); err != nil {
		klog.Errorf("cmd.Wait() error:%s", err.Error())
		return err
	}

	return nil
/*
	waitChan := make(chan error, 1)
	osSignal := cmd.ctx.Value("signal").(chan os.Signal)
	cancelFunc := cmd.ctx.Value("cancelFunc").(context.CancelFunc)
	go func() {
		select {
		case signal := <-osSignal:
			if cmd.sendSignal == nil {
				cmd.sendSignal = cmd.Cmd.Process.Signal
			}
			if err := cmd.sendSignal(signal); err != nil {
				klog.Errorf("send signal to sandbox error: %s", err.Error())
				close(waitChan)
			}
		case <-waitChan:
			return
		}
		timer := time.NewTicker(30 * time.Second)
		select {
		case <-timer.C:
			cancelFunc()
		case <-waitChan:
		}
		timer.Stop()
	}()

	if !daemon {
		if err := cmd.Cmd.Wait(); err != nil {
			return err
		}
	}
	close(waitChan)
	return nil
*/
}
