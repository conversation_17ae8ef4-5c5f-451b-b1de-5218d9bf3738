package config

import (
	"encoding/json"

	"github.com/docker/docker/profiles/seccomp"

	oci "github.com/opencontainers/runtime-spec/specs-go"
)

type LinuxSeccompSyscall struct {
	Names []string              `json:"names,omitempty"`
	Args  []oci.LinuxSeccompArg `json:"args,omitempty"`
}

func setBlockSeccompConfig(blockSyscallList []LinuxSeccompSyscall, config *oci.Spec) error {
	blockTemplate := seccomp.Seccomp{
		LinuxSeccomp: oci.LinuxSeccomp{
			DefaultAction: oci.ActAllow,
		},
		Syscalls: []*seccomp.Syscall{},
	}

	for _, v := range blockSyscallList {
		blockTemplate.Syscalls = append(blockTemplate.Syscalls, &seccomp.Syscall{
			LinuxSyscall: oci.LinuxSyscall{
				Names:  v.Names,
				Args:   v.Args,
				Action: oci.ActErrno,
			},
		})
	}

	configD, err := json.Marshal(blockTemplate)
	if err != nil {
		return err
	}

	blockSeccomp, err := seccomp.LoadProfile(string(configD), config)
	if err != nil {
		return err
	}
	if config.Linux == nil {
		config.Linux = &oci.Linux{}
	}
	config.Linux.Seccomp = blockSeccomp
	return nil
}
