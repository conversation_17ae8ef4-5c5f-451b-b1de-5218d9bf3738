#!/bin/bash

#  Copyright 2011 Google Inc. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#
#  Author: <PERSON> <<EMAIL>>


BASEDIR=`dirname $0`
LIBDIR="${BASEDIR}/../src/.libs"
LIBPATH="${LIBDIR}/@libname@"

if [[ -f "$LIBPATH" ]]
then
  # Include <PERSON><PERSON><PERSON><PERSON><PERSON>'s project build dir if we can find it
  export LD_LIBRARY_PATH="${LIBDIR}:${LD_LIBRARY_PATH}"
else
  # Use the system version instead, w/o requiring ldconfig
  export LD_LIBRARY_PATH="@libdir@:${LD_LIBRARY_PATH}"
fi

# Exec the requested app, replacing this one
LD_PRELOAD="@libname@" exec $@
