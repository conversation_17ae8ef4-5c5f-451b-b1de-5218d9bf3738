package librunlc

import (
	"context"
	"encoding/json"
	"os"
	"os/exec"
	"path/filepath"
	goRuntime "runtime"
	"syscall"

	"code.alipay.com/antjail/antjail/pkg/cgroup"
	"code.alipay.com/antjail/antjail/pkg/runtime"
	"code.alipay.com/antjail/antjail/pkg/util"
	fileUtil "code.alipay.com/antjail/antjail/pkg/util/file"
	"golang.org/x/sys/unix"
	"k8s.io/klog/v2"
)

var ExitCode = 0

type Runlc struct {
}

// Run ...
func (Runlc) Run(ctx context.Context, flags []string, config *runtime.Config, asDaemon bool) {
	// write config to file
	d, err := json.Marshal(config)
	if err != nil {
		klog.Fatal(err)
	}
	/*	fileUtil.CreateRunDir(runtime.Srootdir, config.SecurityConfig.Meta.Name)
		fileUtil.MustWriteContentToFile(filepath.Join(runtime.Srootdir, config.SecurityConfig.Meta.Name, "runlc_config.json"), string(d))
	*/
	ch := make(chan int, 1)
	setnet := make(chan int, 1)
	var cleanFn func() error

	go func() {
		tid := <-ch
		if config.SecurityConfig.Security.Network.Mode == "hostwithpolicy" {
			if err := prepareTUNCharDevice(config); err != nil {
				klog.Fatalf("mount tun device error: %s", err.Error())
			}
			cleanFn = applyNetwork(tid, config)
		}
		setnet <- 1
	}()
	defer func() {
		if cleanFn != nil {
			cleanFn()
		}
		os.Exit(ExitCode)
	}()

	goRuntime.LockOSThread()
	defer goRuntime.UnlockOSThread()

	var oldCpuCgroups string
	var oldMemCgroups string
	if !runtime.Rootless && !runtime.NoCgroup {
		var occ string
		var omc string
		var err error
		if occ, err = cgroup.CurrentCgroupDirectory("cpuset"); err != nil {
			klog.Fatalf("cpuset CurrentCgroupDirectory error:%s", err.Error())
		}
		oldCpuCgroups = occ
		if omc, err = cgroup.CurrentCgroupDirectory("memory"); err != nil {
			klog.Fatalf("memory CurrentCgroupDirectory error:%s", err.Error())
		}
		oldMemCgroups = omc
		klog.V(4).Infof("get antjail cgroup:%s, %s", oldCpuCgroups, oldMemCgroups)
		SetCgroups(config, os.Getpid())
	}

	unshareFlag := syscall.CLONE_NEWNS
	if !runtime.Rootless && pidNamespeceCheck() {
		unshareFlag |= syscall.CLONE_NEWPID
	}
	if config.SecurityConfig.Security.Network.Mode != "host" {
		unshareFlag = unshareFlag | syscall.CLONE_NEWNET
	}
	if err := syscall.Unshare(unshareFlag); err != nil {
		klog.Fatalf("unshare new mount namespace error, detail: %s", err.Error())
	}

	ch <- syscall.Gettid()
	<-setnet

	syscall.Mount("none", "/", "", syscall.MS_REC|syscall.MS_SLAVE, "")

	fileUtil.CreateRunDir(runtime.Srootdir, config.SecurityConfig.Meta.Name)
	fileUtil.MustWriteContentToFile(filepath.Join(runtime.Srootdir, config.SecurityConfig.Meta.Name, "runlc_config.json"), string(d))

	if config.SecurityConfig.Security.Network.Mode == "hostwithpolicy" {
		cleanDNS := applyDNS(config.SecurityConfig.Meta.Name)
		defer cleanDNS()
	}

	if err := syscall.Mount("/", "/", "none", syscall.MS_REMOUNT|syscall.MS_BIND|syscall.MS_SLAVE | getExtraMountFlags("/"), ""); err != nil {
		klog.Fatalf("mount / to mount point error: %s", err.Error())
	}

	//	applyWriteablePath(config)
	//	applyMaskPath(config)

	antjailPath, err := os.Executable()
	if err != nil {
		klog.Fatalf("get antjail exe file path", err.Error())
	}

	/*wd, err := os.Getwd()
	if err != nil {
		klog.Fatalf("get process cwd error, detail: %s", err.Error())
	}*/

	ociConfig, _ := json.Marshal(config.OCIConfig)
	klog.V(4).Info(string(ociConfig))
	secConfig, _ := json.Marshal(config.SecurityConfig)
	klog.V(4).Info(string(secConfig))

	klog.V(4).Info(flags)
	args := []string{
		"runlc_startSandbox", "--containerName", config.SecurityConfig.Meta.Name, "--bundle", filepath.Join(runtime.Srootdir, config.SecurityConfig.Meta.Name)}
	if runtime.Rootless {
		klog.V(4).Info("add rootless flag")
		args = append(args, "--rootless")
	}
	if runtime.NoCgroup {
		klog.V(4).Info("add nocgroup flag")
		args = append(args, "--nocgroup")
	}
	if klog.V(4).Enabled() {
		args = append(args, "-v", "4")
	}
	args = append(args, "--")
	args = append(args, flags...)
	cmd := util.CommandContextWithSignal(ctx, antjailPath, nil, args...)
	if !asDaemon {
		cmd.Stdin = os.Stdin
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr
	}
	cmd.Dir = config.SecurityConfig.System.Cwd
	cmd.Args[0] = "internal_runlc"
	klog.V(4).Infof("cmd.Args: %v", cmd.Args)

	if err := cmd.Start(); err != nil {
		klog.Fatalf("cmd.Start() error: %s", err)
	}

	if !runtime.Rootless && !runtime.NoCgroup {
		klog.V(4).Info("move antjail to cgroup")
		cgroup.MovePidToCgroup(oldCpuCgroups, os.Getpid())
		cgroup.MovePidToCgroup(oldMemCgroups, os.Getpid())
	}
	sandboxName := config.SecurityConfig.Meta.Name
	if err := cmd.Wait(asDaemon); err != nil {
		if !runtime.Rootless && !runtime.NoCgroup {
			RemoveCgroups(oldCpuCgroups, sandboxName)
			RemoveCgroups(oldMemCgroups, sandboxName)
		}
		if exiterr, ok := err.(*exec.ExitError); ok {
			ExitCode = exiterr.ExitCode()
			if config.SecurityConfig.Security.Network.Mode == "hostwithpolicy" {
				return
			}
			os.Exit(exiterr.ExitCode())
		} else {
			klog.Fatalf("cmd.Wait: %v", err)
		}
	}
	if !runtime.Rootless && !runtime.NoCgroup {
		klog.V(4).Info("move antjail out of cgroup")
		RemoveCgroups(oldCpuCgroups, sandboxName)
		RemoveCgroups(oldMemCgroups, sandboxName)
	}

}

func RemoveCgroups(path string, sandboxName string) error {
	name := filepath.Join(path, "antjail-"+sandboxName)
	err := unix.Rmdir(name)
	if err != nil {
		klog.V(4).Infof("rmdir error, name:%s, err:%s", name, err)
	}
	return err
}

func SetCgroups(config *runtime.Config, pid int) {
	if config.SecurityConfig.System.CPU.Number != 0 {
		name := "antjail-" + config.SecurityConfig.Meta.Name
		cgroup.SetMaxCPUNumber(name, int(config.SecurityConfig.System.CPU.Number), pid)
	}
	if config.SecurityConfig.System.Memory.Max != 0 {
		name := "antjail-" + config.SecurityConfig.Meta.Name
		cgroup.SetMaxMemory(name, uint64(config.SecurityConfig.System.Memory.Max), pid)
	}
}
