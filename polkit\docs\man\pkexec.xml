<?xml version="1.0"?>
<!DOCTYPE book PUBLIC "-//OASIS//DTD DocBook XML V4.1.2//EN"
               "http://www.oasis-open.org/docbook/xml/4.1.2/docbookx.dtd" [
<!ENTITY version SYSTEM "../version.xml">
]>
<refentry id="pkexec.1" xmlns:xi="http://www.w3.org/2003/XInclude">
  <refentryinfo>
    <title>pkexec</title>
    <date>May 2009</date>
    <productname>polkit</productname>
  </refentryinfo>

  <refmeta>
    <refentrytitle>pkexec</refentrytitle>
    <manvolnum>1</manvolnum>
    <refmiscinfo class="version"></refmiscinfo>
  </refmeta>

  <refnamediv>
    <refname>pkexec</refname>
    <refpurpose>Execute a command as another user</refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>pkexec</command>
      <arg><option>--version</option></arg>
      <arg><option>--disable-internal-agent</option></arg>
      <arg><option>--help</option></arg>
    </cmdsynopsis>

    <cmdsynopsis>
      <command>pkexec</command>
      <group>
        <arg choice="plain">
          <option>--user</option>
          <replaceable>username</replaceable>
        </arg>
      </group>
      <arg choice="plain"><replaceable>PROGRAM</replaceable></arg>
      <group rep="repeat">
        <arg choice="plain"><replaceable>ARGUMENTS</replaceable></arg>
      </group>
    </cmdsynopsis>

  </refsynopsisdiv>

  <refsect1 id="pkexec-description"><title>DESCRIPTION</title>
    <para>
      <command>pkexec</command> allows an authorized user to execute
      <replaceable>PROGRAM</replaceable> as another user. If
      <replaceable>PROGRAM</replaceable> is not specified, the default
      shell will be run.  If <replaceable>username</replaceable> is
      not specified, then the program will be executed as the
      administrative super user, <emphasis>root</emphasis>.
    </para>
  </refsect1>

  <refsect1 id="pkexec-return-value"><title>RETURN VALUE</title>
    <para>
      Upon successful completion, the return value is the return value
      of <replaceable>PROGRAM</replaceable>. If the calling process is
      not authorized or an authorization could not be obtained through
      authentication or an error occured, <command>pkexec</command>
      exits with a return value of 127. If the authorization could not
      be obtained because the user dismissed the authentication
      dialog, <command>pkexec</command> exits with a return value of
      126.
    </para>
  </refsect1>

  <refsect1 id="pkexec-auth-agent"><title>AUTHENTICATION AGENT</title>
    <para>
      <command>pkexec</command>, like any other polkit application,
      will use the authentication agent registered for the calling
      process or session. However, if no authentication agent is
      available, then <command>pkexec</command> will register its own
      textual authentication agent. This behavior can be turned off by
      passing the <option>--disable-internal-agent</option> option.
    </para>
  </refsect1>

  <refsect1 id="pkexec-security-notes"><title>SECURITY NOTES</title>
    <para>
      Executing a program as another user is a privileged
      operation. By default the action to check for (see
      <xref linkend="pkexec-action"/>) requires administrator
      authentication. In addition, the authentication dialog presented
      to the user will display the full path to the program to be
      executed so the user is aware of what will happen.
    </para>
    <para>
      The environment that <replaceable>PROGRAM</replaceable> will run
      it, will be set to a minimal known and safe environment in order
      to avoid injecting code
      through <literal>LD_LIBRARY_PATH</literal> or similar
      mechanisms. In addition the <literal>PKEXEC_UID</literal>
      environment variable is set to the user id of the process
      invoking <command>pkexec</command>. As a
      result, <command>pkexec</command> will not by default allow you to run
      X11 applications as another user since
      the <literal>$DISPLAY</literal> and <literal>$XAUTHORITY</literal>
      environment variables are not set. These two variables will be retained
      if the <emphasis>org.freedesktop.policykit.exec.allow_gui</emphasis> annotation
      on an action is set to a nonempty value; this is discouraged, though, and
      should only be used for legacy programs.
    </para>

    <para>
      Note that <command>pkexec</command> does no validation of
      the <replaceable>ARGUMENTS</replaceable> passed
      to <replaceable>PROGRAM</replaceable>. In the normal case (where
      administrator authentication is required every
      time <command>pkexec</command> is used), this is not a problem
      since if the user is an administrator he might as well just
      run <command>pkexec bash</command> to get root.
    </para>

    <para>
      However, if an action is used for which the user can retain
      authorization (or if the user is implicitly authorized) this
      could be a security hole. Therefore, as a rule of thumb,
      programs for which the default required authorization is
      changed, should <emphasis role='strong'>never</emphasis> implicitly trust user input (e.g. like any
      other well-written <emphasis>suid</emphasis> program).
    </para>
  </refsect1>

  <refsect1 id="pkexec-action"><title>ACTION AND AUTHORIZATIONS</title>
    <para>
      By default, the
      <emphasis>org.freedesktop.policykit.exec</emphasis> action is
      used. To use another action, use the
      <emphasis>org.freedesktop.policykit.exec.path</emphasis>
      annotation on an action with the value set to the full path of
      the program. In addition to specifying the program, the
      authentication message, description, icon and defaults can be
      specified. If the <emphasis>org.freedesktop.policykit.exec.argv1</emphasis>
      annotation is present, the action will only be picked if the
      first argument to the program matches the value of the annotation.
    </para>
    <para>
      Note that authentication messages may reference variables (see
      <xref linkend="pkexec-variables"/>), for example
      <literal>$(user)</literal> will be expanded to the value of the
      <literal>user</literal> variable.
    </para>
  </refsect1>

  <refsect1 id="pkexec-wrapper"><title>WRAPPER USAGE</title>
    <para>
      To avoid modifying existing software to prefix their
      command-line invocations with <command>pkexec</command>,
      it's possible to use <command>pkexec</command> in a
      <ulink url="http://en.wikipedia.org/wiki/Shebang_(Unix)">she-bang wrapper</ulink>
      like this:
    </para>
    <programlisting><![CDATA[
#!/usr/bin/pkexec /usr/bin/python

import os
import sys

print "Hello, I'm running as uid %d"%(os.getuid())

for n in range(len(sys.argv)):
    print "arg[%d]=`%s'"%(n, sys.argv[n])
]]></programlisting>
    <para>
      If this script is installed into <filename>/usr/bin/my-pk-test</filename>,
      then the following annotations
    </para>
    <programlisting><![CDATA[
  [...]
  <annotate key="org.freedesktop.policykit.exec.path">/usr/bin/python</annotate>
  <annotate key="org.freedesktop.policykit.exec.argv1">/usr/bin/my-pk-test</annotate>
  [...]
]]></programlisting>
    <para>
      can be used to select the appropriate polkit action. Be careful
      to get the latter annotation right, otherwise it will match any
      <command>pkexec</command> invocation of
      <filename>/usr/bin/python</filename> scripts.
    </para>
  </refsect1>

  <refsect1 id="pkexec-variables"><title>VARIABLES</title>
    <para>
      The following variables are set by
      <command>pkexec</command>. They can be used in authorization
      rules and messages shown in authentication dialogs:
    </para>

    <variablelist>
      <varlistentry>
        <term><emphasis>program</emphasis></term>
        <listitem>
          <para>
            Fully qualified path to the program to be executed.
            Example: <quote>/bin/cat</quote>
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term><emphasis>command_line</emphasis></term>
        <listitem>
          <para>
            The requested command-line (do not use this for any
            security checks, it is not secure).
            Example: <quote>cat /srv/xyz/foobar</quote>
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term><emphasis>user</emphasis></term>
        <listitem>
          <para>
            The user name of the user to execute the program as.
            Example: <quote>davidz</quote>
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term><emphasis>user.gecos</emphasis></term>
        <listitem>
          <para>
            The full name of the user to execute the program as.
            Example: <quote>David Zeuthen</quote>
          </para>
        </listitem>
      </varlistentry>
      <varlistentry>
        <term><emphasis>user.display</emphasis></term>
        <listitem>
          <para>
            A representation of the user to execute the program as
            that is suitable for display in an authentication dialog.
            Is typically set to a combination of the user name and the
            full name.
            Example: <quote>David Zeuthen (davidz)</quote>
          </para>
        </listitem>
      </varlistentry>
    </variablelist>

  </refsect1>

  <refsect1 id="pkexec-author"><title>AUTHOR</title>
    <para>
      Written by David Zeuthen <email><EMAIL></email> with
      a lot of help from many others.
    </para>
  </refsect1>

  <refsect1 id="pkexec-bugs">
    <title>BUGS</title>
    <para>
      Please send bug reports to either the distribution or the
      polkit-devel mailing list,
      see the link <ulink url="http://lists.freedesktop.org/mailman/listinfo/polkit-devel"/>
      on how to subscribe.
    </para>
  </refsect1>

  <refsect1 id="pkexec-see-also">
    <title>SEE ALSO</title>
    <para>
      <link linkend="polkit.8"><citerefentry><refentrytitle>polkit</refentrytitle><manvolnum>8</manvolnum></citerefentry></link>,
      <link linkend="polkitd.8"><citerefentry><refentrytitle>polkitd</refentrytitle><manvolnum>8</manvolnum></citerefentry></link>,
      <link linkend="pkaction.1"><citerefentry><refentrytitle>pkaction</refentrytitle><manvolnum>1</manvolnum></citerefentry></link>,
      <link linkend="pkcheck.1"><citerefentry><refentrytitle>pkcheck</refentrytitle><manvolnum>1</manvolnum></citerefentry></link>,
      <link linkend="pkttyagent.1"><citerefentry><refentrytitle>pkttyagent</refentrytitle><manvolnum>1</manvolnum></citerefentry></link>
    </para>
  </refsect1>
</refentry>
