package runlc

import (
	"context"

	"code.alipay.com/antjail/antjail/pkg/librunlc"
	"code.alipay.com/antjail/antjail/pkg/runtime"
)

type runlc struct {
}

func init() {
	runtime.Register(runlc{})
}

const runlcRuntime runtime.Runtime = "runlc"

// Type ...
func (runlc) Type() runtime.Runtime {
	return runlcRuntime
}

// Run ...
func (runlc) Run(ctx context.Context, flags []string, config runtime.Config) {
	lr := librunlc.Runlc{}
	lr.Run(ctx, flags, &config, runtime.Daemon)
}

// SupportMode ...
func (runlc) SupportMode() []string {
	return []string{"daemon", "rootless"}
}
