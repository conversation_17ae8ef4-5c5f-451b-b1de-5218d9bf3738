package file

import (
	"errors"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"syscall"

	"k8s.io/klog/v2"
)

func MustRemoveDir(p string) {
	if err := os.RemoveAll(p); err != nil && !errors.Is(err, os.ErrNotExist) {
		klog.Fatalf("remove dir %s error, detail :%s", p, err.Error())
	}
}

func MustCreateDir(p string) {
	if err := os.MkdirAll(p, fs.ModePerm); err != nil && errors.Is(err, os.ErrNotExist) {
		klog.Fatalf("create dir %s error, detail: %s", p, err.Error())
	}
}

func MustWriteContentToFile(fp, content string) {
	f, err := os.OpenFile(fp, os.O_CREATE|os.O_RDWR, fs.ModePerm)
	if err != nil {
		klog.Fatalf("open file %s error, detail: %s", fp, err.Error())
	}
	defer f.Close()
	n, err := f.WriteString(content)
	if n != len(content) || err != nil {
		klog.Fatal("write content %s to file failed", content, fp)
	}
}

func MustWriteByteToFile(fp string, content []byte) {
	f, err := os.OpenFile(fp, os.O_CREATE|os.O_RDWR, fs.ModePerm)
	if err != nil {
		klog.Fatalf("open file %s error, detail: %s", fp, err.Error())
	}
	defer f.Close()
	n, err := f.Write(content)
	if n != len(content) || err != nil {
		klog.Fatal("write content %s to file failed", content, fp)
	}
}


//This function should be called in a new mount ns
func CreateRunDir(rootDir string, sandboxName string) {
        if _, err := os.Stat(rootDir); os.IsNotExist(err) {
                err := os.MkdirAll(rootDir, fs.ModePerm)
                if err != nil {
                        klog.Fatalf("can't create srootdir: %s %v", rootDir, err)
                        return
                }
		klog.V(4).Info("create rootDir successfully: %s", rootDir)

        } else {
                klog.V(4).Info("the antjail exist")
        }


        if err := syscall.Mount("tmpfs", rootDir, "tmpfs", 0, ""); err != nil {
		klog.Fatal("can't mount tmpfs to %s, error: %s",rootDir, err.Error())
                return
        }

        fp := filepath.Join(rootDir, sandboxName)

        if err := os.MkdirAll(fp, fs.ModePerm); err != nil {
                klog.Fatal("create dir %s error, detail: %s", fp, err.Error())
        }
}


func CopyFilePermission(src string, dst string)  {
	pSrcInfo, err := os.Stat(src)
	if err != nil {
		klog.Fatalf("can't stat %s: %s", src, err.Error())
		return
	}
	srcStat, ok := pSrcInfo.Sys().(*syscall.Stat_t)
	if !ok {
		klog.Fatalf("can't get %s statinfo: %s", src, err.Error())
		return
	}

	if _ , err := os.Stat(dst); os.IsNotExist(err) {
		klog.Fatalf("%s is not exist", dst)
		return
	}

	if err := syscall.Chown(dst, int(srcStat.Uid), int(srcStat.Gid)); err != nil {
		klog.Fatalf("can't change the uid and gid of %s, src: %s, uid:%v, gid:%v, error:%s", dst, src, srcStat.Uid, srcStat.Gid, err.Error())
		return
	}

	if err := syscall.Chmod(dst, uint32(pSrcInfo.Mode().Perm())); err != nil {
		klog.Fatalf("can't change the perm of %s: error:%s", dst, err.Error())
		return
	}
}

func getAllParentsAndSelf(path string) ([]string, error) {
    var paths []string

    // 确保传入的是绝对路径
    absPath, err := filepath.Abs(path)
    if err != nil {
        return nil, fmt.Errorf("can't get abspath, %s: %v", path, err)
    }

    paths = append(paths, absPath)

    // 循环获取父目录
    for absPath != "/" {
        absPath = filepath.Dir(absPath)
        paths = append(paths, absPath)
    }

    // 添加根目录路径
    //paths = append(paths, "/")

    return paths, nil
}


func CopyFilePermissionRecursive(src string, rootfsPath string) {
	srcPaths, err := getAllParentsAndSelf(src)
	if err != nil {
		klog.Fatalf("can't get the parents of %s: %s", src, err.Error())
	}
	for _, v := range srcPaths {
		dst := rootfsPath + v
		CopyFilePermission(v, dst)
	}
}
