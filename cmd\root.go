package cmd

import (
	"bytes"
	"encoding/json"
	goflags "flag"
	"io/fs"
	"os"
	"os/exec"
	"path/filepath"
	"reflect"
	"strings"
	"syscall"

	"code.alipay.com/antjail/antjail/asset"
	_ "code.alipay.com/antjail/antjail/cmd/buildin"
	configPkg "code.alipay.com/antjail/antjail/pkg/config"
	"code.alipay.com/antjail/antjail/pkg/runtime"
	fileUtil "code.alipay.com/antjail/antjail/pkg/util/file"
	kernalUtil "code.alipay.com/antjail/antjail/pkg/util/kernal"

	"golang.org/x/sys/unix"
	"github.com/mitchellh/mapstructure"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"k8s.io/klog/v2"
)

var defaultRuntime = "runsc"

type RootCommandConfig struct {
	Config string
}

var rootCommandConfig RootCommandConfig

func setFlag(rootCmd *cobra.Command) {
	fs := goflags.NewFlagSet("", goflags.PanicOnError)
	klog.InitFlags(fs)
	rootCmd.Flags().AddGoFlagSet(fs)
	rootCmd.PersistentFlags().StringVarP(&rootCommandConfig.Config, "config", "c", "", "security config file")
	rootCmd.PersistentFlags().StringVarP(&runtime.Srootdir, "sroot", "", "", "security config dir, rootless mode default dir /tmp, root mode default /var/run/antjail")
	rootCmd.PersistentFlags().BoolVarP(&runtime.Rootless, "rootless", "", false, "run mode, support rootless, damon")
	rootCmd.PersistentFlags().BoolVarP(&runtime.Daemon, "daemon", "", false, "run mode, support rootless, damon")
	//rootCmd.PersistentFlags().StringVarP(&runtime, "runtime", "rt", "", "runtime, only support runsc")
}

func setConfigFlag(rootCmd *cobra.Command) {
	rootCmd.PersistentFlags().String("version", "", "The config version")
	rootCmd.PersistentFlags().String("meta.name", "", "The config meta name")
	rootCmd.PersistentFlags().String("meta.arg", "", "The config meta arg")
	rootCmd.PersistentFlags().String("meta.log", "", "The config meta log")
	rootCmd.PersistentFlags().String("system.runtime", "runsc", "The config system runtime")
	rootCmd.PersistentFlags().String("system.accel", "systrap", "The config system accel")
	rootCmd.PersistentFlags().String("system.rollback", "", "The config system rollback")
	rootCmd.PersistentFlags().String("system.root.path", "", "The config system root path")
	rootCmd.PersistentFlags().StringSlice("system.root.paths", []string{}, "The config paths combined for root path")
	rootCmd.PersistentFlags().Bool("system.root.readonly", false, "The config system root readonly")
	rootCmd.PersistentFlags().Bool("system.noNewPrivs", true, "no new privs config")
	rootCmd.PersistentFlags().String("system.cwd", "", "the working directory")
	rootCmd.PersistentFlags().Int64("system.memory.max", 0, "The config system memory max")
	rootCmd.PersistentFlags().Int64("system.cpu.number", 0, "The config system cpu number")
	rootCmd.PersistentFlags().StringSlice("security.file.maskedPaths", []string{}, "The config security file maskedPaths")
	rootCmd.PersistentFlags().StringSlice("security.file.writablePaths", []string{}, "The config security file writablePaths")
	rootCmd.PersistentFlags().StringSlice("security.file.writableNoexecPaths", []string{}, "The config security file writableNoexecPaths")
	rootCmd.PersistentFlags().StringSlice("security.file.privatePaths", []string{}, "The config security file writablePaths")
	rootCmd.PersistentFlags().String("security.network.mode", "", "the config security network mode")
	rootCmd.PersistentFlags().IntSlice("security.network.policy.listen", []int{}, "the config security network policy listen")
	rootCmd.PersistentFlags().StringSlice("security.network.policy.tcp", []string{}, "the config security network policy tcp")
	rootCmd.PersistentFlags().StringSlice("security.network.policy.udp", []string{}, "the config security network policy udp")
	rootCmd.PersistentFlags().StringSlice("security.network.policy.dns", []string{}, "the config security network policy dns")

	rootCmd.PersistentFlags().String("security.execve.mode", "", "the config security execve mode")
	rootCmd.PersistentFlags().StringSlice("security.execve.paths", []string{}, "the config security execve paths")

	//	rootCmd.PersistentFlags().String("idMapping.type", "", "the uid and gid map type")

	rootCmd.PersistentFlags().StringSlice("cmd", []string{}, "the config entrypoint cmd")
	rootCmd.PersistentFlags().StringSlice("security.cap.add", []string{}, "The security cap add for sandbox")
	rootCmd.PersistentFlags().StringSlice("security.cap.drop", []string{}, "The security cap drop for sandbox")
	rootCmd.PersistentFlags().String("security.seccomp.mode", "", "The seccomp mode for sandbox")
	rootCmd.PersistentFlags().String("security.seccomp.syscallList", "", "The seccomp block or allow syscall list for sandbox")

	rootCmd.PersistentFlags().String("system.rollbackOverwrite", "", "when rollback to runlc, overwrite origin config")
	viper.BindPFlags(rootCmd.Flags())
	viper.BindPFlags(rootCmd.PersistentFlags())
}

func RunAsRoot() {
	klog.V(4).Info("re-running self")
	cmd := exec.Command("/proc/self/exe", os.Args[1:]...)

	cmd.SysProcAttr = &unix.SysProcAttr{
		Cloneflags: unix.CLONE_NEWUSER | unix.CLONE_NEWNS,
		// Set current user/group as root inside the namespace. Since we may not
		// have CAP_SETUID/CAP_SETGID, just map root to the current user/group.
		UidMappings: []syscall.SysProcIDMap{
			{ContainerID: 0, HostID: os.Getuid(), Size: 1},
		},
		GidMappings: []syscall.SysProcIDMap{
			{ContainerID: 0, HostID: os.Getgid(), Size: 1},
		},
		Credential:                 &syscall.Credential{Uid: 0, Gid: 0},
		GidMappingsEnableSetgroups: false,

		// Make sure child is killed when the parent terminates.
		Pdeathsig: unix.SIGKILL,

		// Detach from session. Otherwise, signals sent to the foreground process
		// will also be forwarded by this process, resulting in duplicate signals.
		Setsid: true,
	}

	cmd.Env = os.Environ()
	cmd.Stdin = os.Stdin
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	if err := cmd.Start(); err != nil {
		klog.Fatalf("re-executing self: %s", err.Error())
	}

	if err := cmd.Wait(); err != nil {
		if exiterr, ok := err.(*exec.ExitError); ok {
			os.Exit(exiterr.ExitCode())
		} else {
			klog.Fatalf("cmd.Wait: %v", err)
		}
	}

	
}
func NewRootCommand() *cobra.Command {
	rootCmd := &cobra.Command{
		Use:     "antjail [flags] command",
		Short:   "antjail是一款基于gvisor的进程级沙箱",
		Example: "antjail -c /home/<USER>/security.json ping www.alipay.com",
		Long:    "antjail是一款基于gvisor的进程级沙箱，本质上是将进程运行在一个轻量级的虚拟机中。antjail使用的场景为需要执行一些不受信任的脚本或者程序，通过配置指定的网络策略以及文件系统策略，可以控制被沙箱进程的行为，防止其执行恶意代码。",
		PreRun: func(cmd *cobra.Command, args []string) {
			// clear antjail-* cgroup
			clearAntjailCgroup()
		},
		Args: func(cmd *cobra.Command, args []string) error { return nil },
		Run: func(cmd *cobra.Command, args []string) {
			var cmdArg string
			for _, arg := range os.Args {
				cmdArg = cmdArg + " " + arg
			}
			klog.V(4).Info("argument: %s", cmdArg)
/*
			euid := os.Geteuid()
			if runtime.Rootless && (euid != 0) {
				RunAsRoot()
				os.Exit(0)
			}
			*/
			if rootCommandConfig.Config != "" {
				klog.V(4).Info("get config from cmdline")
				viper.SetConfigFile(rootCommandConfig.Config)
				if err := viper.ReadInConfig(); err != nil {
					klog.Fatal(err)
				}
			} else {
				klog.V(4).Info("get config from embed.fs")
				// get config template from embed.fs
				configTemplate, err := asset.RunscBinary.ReadFile("security.json")
				if err != nil {
					klog.Fatal(err)
				}
				viper.SetConfigType("json")
				if err := viper.ReadConfig(bytes.NewReader(configTemplate)); err != nil {
					klog.Fatal(err)
				}
			}
			config := configPkg.SecurityConfig{}
			if err := viper.Unmarshal(&config, customUnmarshalHook); err != nil {
				klog.Fatal(err)
			}
			klog.V(4).Infof("origin config:%+v", config)
			if config.System.Runtime == "" {
				config.System.Runtime = defaultRuntime
			}
			if config.Meta.Log != "" {
				klog.LogToStderr(false)
				fileUtil.MustCreateDir(config.Meta.Log)
				klog.SetLogToDir(config.Meta.Log)
			}
                        euid := os.Geteuid()
			uid := os.Getuid()
                        if runtime.Rootless && (euid != uid) {
				if config.System.Runtime == "runlc" && kernalUtil.KernelLower49() != nil {
					klog.Fatalf("this kernel version doesn't support runlc rootless mode")
				}
                                RunAsRoot()
                                os.Exit(0)
                        }
			klog.V(4).Info("start run")
			runtime.Run(runtime.Runtime(config.System.Runtime), args, config)
		},
	}
	setFlag(rootCmd)
	setConfigFlag(rootCmd)
	rootCmd.CompletionOptions.DisableDefaultCmd = true
	rootCmd.AddCommand(newVersionCommand())
	rootCmd.AddCommand(newSpecCommand())
	rootCmd.AddCommand(newRunlcSandBox())
	return rootCmd
}

func clearAntjailCgroup() {
	filepath.WalkDir("/sys/fs/cgroup", func(path string, d fs.DirEntry, err error) error {
		if os.IsNotExist(err) {
			return nil
		} else if err != nil {
			klog.V(4).Info("get cgroup dir error, detail: %s", err.Error())
			return err
		}
		if !d.IsDir() {
			return nil
		}
		if strings.HasPrefix(d.Name(), "antjail-") {
			os.RemoveAll(path)
		}
		return nil
	})
}

func customUnmarshalHook(dc *mapstructure.DecoderConfig) {
	dc.DecodeHook = func(f reflect.Type, t reflect.Type, data interface{}) (interface{}, error) {
		if f.Kind() != reflect.String {
			return data, nil
		}
		d := data.(string)
		if t == reflect.TypeOf([]configPkg.LinuxSeccompSyscall{}) {
			target := []configPkg.LinuxSeccompSyscall{}
			if d == "" {
				return target, nil
			}
			if err := json.Unmarshal([]byte(d), &target); err != nil {
				return target, err
			}
			return target, nil
		}


		rollbackOverwriteConfig := make(map[string]interface{}, 0)

		if t == reflect.TypeOf(rollbackOverwriteConfig) {
			if d == "" {
				return rollbackOverwriteConfig, nil
			}
			if err := json.Unmarshal([]byte(d), &rollbackOverwriteConfig); err != nil {
				return rollbackOverwriteConfig, err
			}
			return rollbackOverwriteConfig, nil
		}
		return data, nil
	}
}
