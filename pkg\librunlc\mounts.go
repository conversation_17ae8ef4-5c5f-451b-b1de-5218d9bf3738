package librunlc

import (
	"syscall"

	"code.alipay.com/antjail/antjail/pkg/util/file"
	"k8s.io/klog/v2"
)

type mountInfo struct {
	Target string
	FSType string
	Source string
	Flags  []string
}

func mountDefaultFSWithForkInPidNamesapce() error {
	defaultMounts := []file.MountInfo{
		{
			Target: "/proc",
			FSType: "proc",
			Source: "proc",
			Flags:  syscall.MS_NOSUID | syscall.MS_NOEXEC | syscall.MS_NODEV,
		},
	}
	if err := syscall.Mount("none", "/proc", "", syscall.MS_REC|syscall.MS_PRIVATE, ""); err != nil {
		klog.Fatal("Mount /proc in new pid and mount namespace as private error: %s", err.Error())
	}

	/*file.MountInfo{
		Target: "/sys",
		FSType: "sysfs",
		Source: "sysfs",
		Flags:  syscall.MS_NOSUID | syscall.MS_NOEXEC | syscall.MS_NODEV | syscall.MS_RDONLY,
	},
	file.MountInfo{
		Target: "/dev/pts",
		FSType: "devpts",
		Source: "devpts",
		Flags:  syscall.MS_NOSUID | syscall.MS_NOEXEC,
		Data:   "ptmxmode=0666,mode=0620,gid=5",
	},
	file.MountInfo{
		Target: "/dev/shm",
		FSType: "tmpfs",
		Source: "shm",
		Flags:  syscall.MS_NOSUID | syscall.MS_NOEXEC | syscall.MS_NODEV,
		Data:   "mode=1777,size=65536k",
	},
	file.MountInfo{
		Target: "/dev/mqueue",
		FSType: "mqueue",
		Source: "mqueue",
		Flags:  syscall.MS_NOSUID | syscall.MS_NOEXEC | syscall.MS_NODEV,
	},*/
	for _, v := range defaultMounts {
		if err := file.Mount(v); err != nil {
			klog.Fatalf("Mount source %s to target %s error: %s", v.Source, v.Target, err.Error())
		}
	}
	return nil

}
