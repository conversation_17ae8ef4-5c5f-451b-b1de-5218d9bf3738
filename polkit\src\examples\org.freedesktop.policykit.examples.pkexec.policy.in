<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE policyconfig PUBLIC "-//freedesktop//DTD polkit Policy Configuration 1.0//EN"
"http://www.freedesktop.org/software/polkit/policyconfig-1.dtd">
<policyconfig>

  <vendor>Examples for the polkit project</vendor>
  <vendor_url>http://www.freedesktop.org/wiki/Software/polkit/</vendor_url>

  <action id="org.freedesktop.policykit.example.pkexec.run-frobnicate">
    <_description>Run the polkit example program Frobnicate</_description>
    <_message>Authentication is required to run the polkit example program Frobnicate (user=$(user), user.gecos=$(user.gecos), user.display=$(user.display), program=$(program), command_line=$(command_line))</_message>
    <icon_name>audio-x-generic</icon_name> <!-- just an example -->
    <defaults>
      <allow_any>no</allow_any>
      <allow_inactive>no</allow_inactive>
      <allow_active>auth_admin_keep</allow_active>
    </defaults>
    <annotate key="org.freedesktop.policykit.exec.path">/usr/bin/pk-example-frobnicate</annotate>
  </action>

</policyconfig>
