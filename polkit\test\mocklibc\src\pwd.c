/**
 * Copyright 2011 Google Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Author: <PERSON> <<EMAIL>>
 */

#include <pwd.h>

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/types.h>

#define PASSWD_CONFIG_KEY "MOCK_PASSWD"

static FILE *global_stream = NULL;

void setpwent(void) {
  if (global_stream)
    endpwent();

  const char *path = getenv(PASSWD_CONFIG_KEY);
  if (!path)
    return;

  global_stream = fopen(path, "r");
}

struct passwd *getpwent(void) {
  if (!global_stream)
    setpwent();

  if (!global_stream)
    return NULL;

  return fgetpwent(global_stream);
}

void endpwent(void) {
  if (!global_stream)
    return;

  fclose(global_stream);
  global_stream = NULL;
}

struct passwd *getpwnam(const char *name) {
  const char *path = getenv(PASSWD_CONFIG_KEY);
  if (!path)
    return NULL;

  FILE *stream = fopen(path, "r");
  if (!stream)
    return NULL;

  struct passwd *entry;
  while ((entry = fgetpwent(stream))) {
    if (strcmp(entry->pw_name, name) == 0) {
      fclose(stream);
      return entry;
    }
  }

  fclose(stream);
  return NULL;
}

struct passwd *getpwuid(uid_t uid) {
  const char *path = getenv(PASSWD_CONFIG_KEY);
  if (!path)
    return NULL;

  FILE *stream = fopen(path, "r");
  if (!stream)
    return NULL;

  struct passwd *entry;
  while ((entry = fgetpwent(stream))) {
    if (entry->pw_uid == uid) {
      fclose(stream);
      return entry;
    }
  }

  fclose(stream);
  return NULL;
}
