# Hungarian translation for polkit
# Copyright (C) 2016. Free Software Foundation, Inc.
# This file is distributed under the same license as the polkit package.
#
# <PERSON><PERSON><PERSON> <kelemeng at ubuntu dot com>, 2016.
msgid ""
msgstr ""
"Project-Id-Version: polkit\n"
"Report-Msgid-Bugs-To: https://bugs.freedesktop.org/enter_bug.cgi?"
"product=PolicyKit&keywords=I18N+L10N&component=libpolkit\n"
"POT-Creation-Date: 2016-01-23 02:19+0000\n"
"PO-Revision-Date: 2016-01-23 14:43+0100\n"
"Last-Translator: <PERSON><PERSON><PERSON> <kelemeng at ubuntu dot com>\n"
"Language-Team: Hungarian <<EMAIL>>\n"
"Language: hu\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Lokalize 1.5\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: ../actions/org.freedesktop.policykit.policy.in.h:1
msgid "Run a program as another user"
msgstr "Program futtatása másik felhasználóként"

#: ../actions/org.freedesktop.policykit.policy.in.h:2
msgid "Authentication is required to run a program as another user"
msgstr "Hitelesítés szükséges a program futtatásához másik felhasználóként"

#: ../src/examples/org.freedesktop.policykit.examples.pkexec.policy.in.h:1
msgid "Run the polkit example program Frobnicate"
msgstr "Frobnicate polkit példaprogram futtatása"

#: ../src/examples/org.freedesktop.policykit.examples.pkexec.policy.in.h:2
msgid ""
"Authentication is required to run the polkit example program Frobnicate "
"(user=$(user), user.gecos=$(user.gecos), user.display=$(user.display), "
"program=$(program), command_line=$(command_line))"
msgstr ""
"Hitelesítés szükséges a Frobnicate polkit példaprogram futtatásához "
"(user=$(user), user.gecos=$(user.gecos), user.display=$(user.display), "
"program=$(program), command_line=$(command_line))"

#: ../src/programs/pkaction.c:100
msgid "Only output information about ACTION"
msgstr "Csak a MŰVELETRŐL írjon ki információkat"

#: ../src/programs/pkaction.c:100
msgid "ACTION"
msgstr "MŰVELET"

#: ../src/programs/pkaction.c:104
msgid "Output detailed action information"
msgstr "Részletes műveletinformációk megjelenítése"

#: ../src/programs/pkaction.c:108 ../src/programs/pkttyagent.c:61
msgid "Show version"
msgstr "Verziószám megjelenítése"

#: ../src/programs/pkaction.c:130
msgid "[--action-id ACTION]"
msgstr "[--action-id MŰVELET]"

#: ../src/programs/pkaction.c:131 ../src/programs/pkttyagent.c:81
#, c-format
msgid ""
"Report bugs to: %s\n"
"%s home page: <%s>"
msgstr ""
"A hibák itt jelenthetők: %s\n"
"A %s honlapja: <%s>"

#: ../src/programs/pkaction.c:145 ../src/programs/pkcheck.c:491
#: ../src/programs/pkttyagent.c:95
#, c-format
msgid "%s: Unexpected argument `%s'\n"
msgstr "%s: váratlan argumentum: „%s”\n"

#: ../src/programs/pkcheck.c:35
#, c-format
msgid ""
"Usage:\n"
"  pkcheck [OPTION...]\n"
"\n"
"Help Options:\n"
"  -h, --help                         Show help options\n"
"\n"
"Application Options:\n"
"  -a, --action-id=ACTION             Check authorization to perform ACTION\n"
"  -u, --allow-user-interaction       Interact with the user if necessary\n"
"  -d, --details=KEY VALUE            Add (KEY, VALUE) to information about "
"the action\n"
"  --enable-internal-agent            Use an internal authentication agent if "
"necessary\n"
"  --list-temp                        List temporary authorizations for "
"current session\n"
"  -p, --process=PID[,START_TIME,UID] Check authorization of specified "
"process\n"
"  --revoke-temp                      Revoke all temporary authorizations for "
"current session\n"
"  -s, --system-bus-name=BUS_NAME     Check authorization of owner of "
"BUS_NAME\n"
"  --version                          Show version\n"
"\n"
"Report bugs to: %s\n"
"%s home page: <%s>\n"
msgstr ""
"Használat:\n"
"  pkcheck [KAPCSOLÓ…]\n"
"\n"
"Súgó kapcsolói:\n"
"  -h, --help                         Súgókapcsolók megjelenítése\n"
"\n"
"Alkalmazás kapcsolói:\n"
"  -a, --action-id=MŰVELET            A MŰVELET elvégzésére való "
"felhatalmazás\n"
"                                       ellenőrzése\n"
"  -u, --allow-user-interaction       Felhasználóval való párbeszéd\n"
"                                       engedélyezése, ha szükséges\n"
"  -d, --details=KULCS ÉRTÉK          A (KULCS, ÉRTÉK) hozzáadása a "
"művelettel\n"
"                                       kapcsolatos információkhoz\n"
"  --enable-internal-agent            Belső hitelesítési ügynök használata, "
"ha\n"
"                                       szükséges\n"
"  --list-temp                        Ideiglenes felhatalmazások felsorolása "
"az\n"
"                                       aktuális munkamenethez\n"
"  -p, --process=PID[,INDÍTÁSI_IDŐ,UID]  A megadott folyamat "
"felhatalmazásának\n"
"                                          ellenőrzése\n"
"  --revoke-temp                      Minden ideiglenes felhatalmazás "
"visszavonása\n"
" az aktuális munkamenetből\n"
"  -s, --system-bus-name=BUSZNÉV      A BUSZNÉV tulajdonosának "
"felhatalmazásának\n"
"                                       ellenőrzése\n"
"  --version                          Verziószám kiírása\n"
"\n"
"A hibák itt jelenthetők: %s\n"
"A %s honlapja: <%s>\n"

#: ../src/programs/pkcheck.c:391 ../src/programs/pkcheck.c:424
#: ../src/programs/pkcheck.c:436
#, c-format
msgid "%s: Argument expected after `%s'\n"
msgstr "%s: argumentum szükséges a következő után: „%s”\n"

#: ../src/programs/pkcheck.c:414
#, c-format
msgid "%s: Invalid --process value `%s'\n"
msgstr "%s: Érvénytelen --process érték: „%s”\n"

#: ../src/programs/pkcheck.c:451 ../src/programs/pkcheck.c:460
#, c-format
msgid "%s: Two arguments expected after `--detail'\n"
msgstr "%s: Két argumentum szükséges a „--detail” után\n"

#: ../src/programs/pkcheck.c:521
#, c-format
msgid "%s: Subject not specified\n"
msgstr "%s: Nincs megadva az alany\n"

#. Translators: message shown when trying to run a program as root. Do not
#. * translate the $(program) fragment - it will be expanded to the path
#. * of the program e.g.  /bin/bash.
#.
#: ../src/programs/pkexec.c:794
msgid "Authentication is needed to run `$(program)' as the super user"
msgstr "Hitelesítés szükséges a(z) „$(program)” futtatásához rendszergazdaként"

#. Translators: message shown when trying to run a program as another user.
#. * Do not translate the $(program) or $(user) fragments - the former will
#. * be expanded to the path of the program e.g. "/bin/bash" and the latter
#. * to the user e.g. "John Doe (johndoe)" or "johndoe".
#.
#: ../src/programs/pkexec.c:804
msgid "Authentication is needed to run `$(program)' as user $(user.display)"
msgstr ""
"Hitelesítés szükséges a(z) „$(program)” futtatásához $(user.display) "
"felhasználóként"

#: ../src/programs/pkttyagent.c:44
msgid "Don't replace existing agent if any"
msgstr "Ne cserélje a meglévő ügynököt, ha van"

#: ../src/programs/pkttyagent.c:48
msgid "Close FD when the agent is registered"
msgstr "Fájlleíró lezárása az ügynök regisztrálásakor"

#: ../src/programs/pkttyagent.c:48
msgid "FD"
msgstr "FD"

#: ../src/programs/pkttyagent.c:52
msgid "Register the agent for the specified process"
msgstr "Az ügynök regisztrálása a megadott folyamathoz"

#: ../src/programs/pkttyagent.c:53
msgid "PID[,START_TIME]"
msgstr "PID[,INDÍTÁSI_IDŐ]"

#: ../src/programs/pkttyagent.c:57
msgid "Register the agent owner of BUS_NAME"
msgstr "Az ügynök regisztrálása a BUSZNÉV tulajdonosaként"

#: ../src/programs/pkttyagent.c:57
msgid "BUS_NAME"
msgstr "BUSZNÉV"

#: ../src/programs/pkttyagent.c:127
#, c-format
msgid "%s: Invalid process specifier `%s'\n"
msgstr "%s: Érvénytelen folyamatmegadás: „%s”\n"

#~ msgid "Configure lock down for an action"
#~ msgstr "Művelet zárolásának beállítása"

#~ msgid "Authentication is required to configure lock down policy"
#~ msgstr "Hitelesítés szükséges a zárolási házirend beállításáhozv"
