package cmd

import (
	"bytes"
	"encoding/json"
	"fmt"

	"code.alipay.com/antjail/antjail/asset"
	"code.alipay.com/antjail/antjail/pkg/config"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"k8s.io/klog/v2"
)


func newSpecCommand() *cobra.Command {
	specCmd := &cobra.Command{
		Use: "spec",
		Short: "generate securityConfig.json from command line args",
		Run: func(cmd *cobra.Command, _ []string) {
			if rootCommandConfig.Config != "" {
				viper.SetConfigFile(rootCommandConfig.Config)
				if err := viper.ReadInConfig(); err != nil {
					klog.Fatal(err)
				}
			} else {
				// get config template from embed.fs
				configTemplate, err := asset.RunscBinary.ReadFile("security.json")
				if err != nil {
					klog.Fatal(err)
				}
				viper.SetConfigType("json")
				if err := viper.ReadConfig(bytes.NewReader(configTemplate)); err != nil {
					klog.Fatal(err)
				}
			}
			config := config.SecurityConfig{}
			if err := viper.Unmarshal(&config); err != nil {
				klog.Fatal(err)
			}
			if config.System.Runtime == "" {
				config.System.Runtime = defaultRuntime
			}
			d, err := json.Marshal(config)
			if err != nil {
				klog.Fatal(err)
			}
			fmt.Println(string(d))
		},
	}
	// generate by chatgpt
	return specCmd
}
