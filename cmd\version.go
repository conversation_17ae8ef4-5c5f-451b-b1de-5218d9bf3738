package cmd

import (
	"fmt"

	"github.com/spf13/cobra"
)


var (
	// RELEASE returns the release version
	RELEASE = "UNKNOWN"
	// REPO returns the git repository URL
	REPO = "UNKNOWN"
	// COMMIT returns the short sha from git
	COMMIT     = "UNKNOWN"
)

// String returns information about the release.
func version() string {
	return fmt.Sprintf(`-------------------------------------------------------------------------------
  AntJail
  Release:       %s
  Build:         %s
  Repository:    %s
-------------------------------------------------------------------------------
`, RELEASE, COMMIT, REPO)
}

func newVersionCommand() *cobra.Command {
	return &cobra.Command{
		Use:   "version",
		Short: "show antjail version",
		Run: func(_ *cobra.Command, _ []string) {
			fmt.Println(version())
		},
	}
}
