package runsc

import (
	"fmt"
	"os"
	"os/exec"
	"syscall"

	"code.alipay.com/antjail/antjail/pkg/runtime"
	"k8s.io/klog/v2"
)

const rootDir = "/var/run/runsc"

func (r *runsc) sendSignalToSandbox(config runtime.Config) func(os.Signal) error {
	return func(signal os.Signal) error {
		syscallSig := signal.(syscall.Signal)
		// get sig string
		ss, ok := signalMap[int(syscallSig)]
		if !ok {
			return fmt.Errorf("not found signal %d", int(syscallSig))
		}

		args := []string{}

		if klog.V(4).Enabled() {
			args = append(args, "-alsologtostderr", "-debug")
		}
		args = append(args, "kill", config.SecurityConfig.Meta.Name, ss)

		cmd := exec.Command(r.runscfile, args...)
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr
		cmd.Stdin = os.Stdin
		cmd.Dir, _ = os.Getwd()
		klog.V(4).Info(cmd.String())

		return cmd.Run()
	}
}

var signalMap = map[int]string{
	int(syscall.SIGINT):  "INT",
	int(syscall.SIGQUIT): "QUIT",
	int(syscall.SIGTERM): "TERM",
	int(syscall.SIGHUP):  "HUP",
}
