/* -*- mode: js; js-indent-level: 4; indent-tabs-mode: nil -*- */

/* see test/polkitbackend/test-polkitbackendjsauthority.c */

/* NOTE: this is the /usr/share/polkit-1/rules.d version of 10-testing.rules */

polkit.addRule(function(action, subject) {
    if (action.id == "net.company.order0") {
        return polkit.Result.NO; // earlier rule should win
    }
});

polkit.addRule(function(action, subject) {
    if (action.id == "net.company.order1") {
        return polkit.Result.YES;
    }
});
