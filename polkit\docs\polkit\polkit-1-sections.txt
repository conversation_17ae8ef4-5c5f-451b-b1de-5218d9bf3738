<SECTION>
<FILE>polkitunixuser</FILE>
PolkitUnixUser
polkit_unix_user_new
polkit_unix_user_new_for_name
polkit_unix_user_get_uid
polkit_unix_user_set_uid
polkit_unix_user_get_name
<SUBSECTION Standard>
PolkitUnixUserClass
POLKIT_UNIX_USER
POLKIT_IS_UNIX_USER
POLKIT_TYPE_UNIX_USER
polkit_unix_user_get_type
POLKIT_UNIX_USER_CLASS
POLKIT_IS_UNIX_USER_CLASS
POLKIT_UNIX_USER_GET_CLASS
</SECTION>

<SECTION>
<FILE>polkitauthority</FILE>
PolkitAuthority
PolkitAuthorityFeatures
PolkitCheckAuthorizationFlags
polkit_authority_get_async
polkit_authority_get_finish
polkit_authority_get_sync
polkit_authority_get
polkit_authority_get_owner
polkit_authority_get_backend_name
polkit_authority_get_backend_version
polkit_authority_get_backend_features
polkit_authority_check_authorization
polkit_authority_check_authorization_finish
polkit_authority_check_authorization_sync
polkit_authority_enumerate_actions
polkit_authority_enumerate_actions_finish
polkit_authority_enumerate_actions_sync
polkit_authority_register_authentication_agent
polkit_authority_register_authentication_agent_finish
polkit_authority_register_authentication_agent_sync
polkit_authority_register_authentication_agent_with_options
polkit_authority_register_authentication_agent_with_options_finish
polkit_authority_register_authentication_agent_with_options_sync
polkit_authority_unregister_authentication_agent
polkit_authority_unregister_authentication_agent_finish
polkit_authority_unregister_authentication_agent_sync
polkit_authority_authentication_agent_response
polkit_authority_authentication_agent_response_finish
polkit_authority_authentication_agent_response_sync
polkit_authority_enumerate_temporary_authorizations
polkit_authority_enumerate_temporary_authorizations_finish
polkit_authority_enumerate_temporary_authorizations_sync
polkit_authority_revoke_temporary_authorizations
polkit_authority_revoke_temporary_authorizations_finish
polkit_authority_revoke_temporary_authorizations_sync
polkit_authority_revoke_temporary_authorization_by_id
polkit_authority_revoke_temporary_authorization_by_id_finish
polkit_authority_revoke_temporary_authorization_by_id_sync
<SUBSECTION Standard>
PolkitAuthorityClass
POLKIT_AUTHORITY
POLKIT_IS_AUTHORITY
POLKIT_TYPE_AUTHORITY
polkit_authority_get_type
POLKIT_AUTHORITY_CLASS
POLKIT_IS_AUTHORITY_CLASS
POLKIT_AUTHORITY_GET_CLASS
polkit_check_authorization_flags_get_type
</SECTION>

<SECTION>
<FILE>polkitauthorizationresult</FILE>
PolkitAuthorizationResult
polkit_authorization_result_new
polkit_authorization_result_get_is_authorized
polkit_authorization_result_get_is_challenge
polkit_authorization_result_get_retains_authorization
polkit_authorization_result_get_temporary_authorization_id
polkit_authorization_result_get_dismissed
polkit_authorization_result_get_details
<SUBSECTION Standard>
PolkitAuthorizationResultClass
POLKIT_AUTHORIZATION_RESULT
POLKIT_IS_AUTHORIZATION_RESULT
POLKIT_TYPE_AUTHORIZATION_RESULT
polkit_authorization_result_get_type
POLKIT_AUTHORIZATION_RESULT_CLASS
POLKIT_IS_AUTHORIZATION_RESULT_CLASS
POLKIT_AUTHORIZATION_RESULT_GET_CLASS
</SECTION>

<SECTION>
<FILE>polkitsystembusname</FILE>
PolkitSystemBusName
polkit_system_bus_name_new
polkit_system_bus_name_get_name
polkit_system_bus_name_set_name
polkit_system_bus_name_get_process_sync
<SUBSECTION Standard>
PolkitSystemBusNameClass
POLKIT_SYSTEM_BUS_NAME
POLKIT_IS_SYSTEM_BUS_NAME
POLKIT_TYPE_SYSTEM_BUS_NAME
polkit_system_bus_name_get_type
POLKIT_SYSTEM_BUS_NAME_CLASS
POLKIT_IS_SYSTEM_BUS_NAME_CLASS
POLKIT_SYSTEM_BUS_NAME_GET_CLASS
</SECTION>

<SECTION>
<FILE>polkitunixgroup</FILE>
PolkitUnixGroup
polkit_unix_group_new
polkit_unix_group_new_for_name
polkit_unix_group_get_gid
polkit_unix_group_set_gid
<SUBSECTION Standard>
PolkitUnixGroupClass
POLKIT_UNIX_GROUP
POLKIT_IS_UNIX_GROUP
POLKIT_TYPE_UNIX_GROUP
polkit_unix_group_get_type
POLKIT_UNIX_GROUP_CLASS
POLKIT_IS_UNIX_GROUP_CLASS
POLKIT_UNIX_GROUP_GET_CLASS
</SECTION>

<SECTION>
<FILE>polkitunixnetgroup</FILE>
PolkitUnixNetgroup
polkit_unix_netgroup_new
polkit_unix_netgroup_get_name
polkit_unix_netgroup_set_name
<SUBSECTION Standard>
PolkitUnixNetgroupClass
POLKIT_UNIX_NETGROUP
POLKIT_IS_UNIX_NETGROUP
POLKIT_TYPE_UNIX_NETGROUP
polkit_unix_netgroup_get_type
POLKIT_UNIX_NETGROUP_CLASS
POLKIT_IS_UNIX_NETGROUP_CLASS
POLKIT_UNIX_NETGROUP_GET_CLASS
</SECTION>

<SECTION>
<FILE>polkitunixsession</FILE>
PolkitUnixSession
polkit_unix_session_new
polkit_unix_session_new_for_process
polkit_unix_session_new_for_process_finish
polkit_unix_session_new_for_process_sync
polkit_unix_session_get_session_id
polkit_unix_session_set_session_id
<SUBSECTION Standard>
PolkitUnixSessionClass
POLKIT_UNIX_SESSION
POLKIT_IS_UNIX_SESSION
POLKIT_TYPE_UNIX_SESSION
polkit_unix_session_get_type
POLKIT_UNIX_SESSION_CLASS
POLKIT_IS_UNIX_SESSION_CLASS
POLKIT_UNIX_SESSION_GET_CLASS
</SECTION>

<SECTION>
<FILE>polkitunixprocess</FILE>
PolkitUnixProcess
polkit_unix_process_new
polkit_unix_process_new_full
polkit_unix_process_new_for_owner
polkit_unix_process_set_pid
polkit_unix_process_get_pid
polkit_unix_process_set_start_time
polkit_unix_process_get_start_time
polkit_unix_process_set_uid
polkit_unix_process_get_uid
polkit_unix_process_get_owner
<SUBSECTION Standard>
PolkitUnixProcessClass
POLKIT_UNIX_PROCESS
POLKIT_IS_UNIX_PROCESS
POLKIT_TYPE_UNIX_PROCESS
polkit_unix_process_get_type
POLKIT_UNIX_PROCESS_CLASS
POLKIT_IS_UNIX_PROCESS_CLASS
POLKIT_UNIX_PROCESS_GET_CLASS
</SECTION>

<SECTION>
<FILE>polkitidentity</FILE>
PolkitIdentity
PolkitIdentityIface
polkit_identity_hash
polkit_identity_equal
polkit_identity_to_string
polkit_identity_from_string
<SUBSECTION Standard>
POLKIT_IDENTITY
POLKIT_IS_IDENTITY
POLKIT_TYPE_IDENTITY
polkit_identity_get_type
POLKIT_IDENTITY_GET_IFACE
</SECTION>

<SECTION>
<FILE>polkitsubject</FILE>
PolkitSubject
PolkitSubjectIface
polkit_subject_hash
polkit_subject_equal
polkit_subject_exists
polkit_subject_exists_finish
polkit_subject_exists_sync
polkit_subject_to_string
polkit_subject_from_string
<SUBSECTION Standard>
POLKIT_SUBJECT
POLKIT_IS_SUBJECT
POLKIT_TYPE_SUBJECT
polkit_subject_get_type
POLKIT_SUBJECT_GET_IFACE
</SECTION>

<SECTION>
<FILE>polkitactiondescription</FILE>
PolkitActionDescription
PolkitImplicitAuthorization
polkit_action_description_get_action_id
polkit_action_description_get_description
polkit_action_description_get_message
polkit_action_description_get_vendor_name
polkit_action_description_get_vendor_url
polkit_action_description_get_icon_name
polkit_action_description_get_implicit_any
polkit_action_description_get_implicit_inactive
polkit_action_description_get_implicit_active
polkit_action_description_get_annotation
polkit_action_description_get_annotation_keys
polkit_implicit_authorization_to_string
polkit_implicit_authorization_from_string
<SUBSECTION Standard>
PolkitActionDescriptionClass
POLKIT_ACTION_DESCRIPTION
POLKIT_IS_ACTION_DESCRIPTION
POLKIT_TYPE_ACTION_DESCRIPTION
polkit_action_description_get_type
POLKIT_ACTION_DESCRIPTION_CLASS
POLKIT_IS_ACTION_DESCRIPTION_CLASS
POLKIT_ACTION_DESCRIPTION_GET_CLASS
polkit_implicit_authorization_get_type
</SECTION>

<SECTION>
<FILE>polkiterror</FILE>
POLKIT_ERROR
PolkitError
<SUBSECTION Standard>
polkit_error_quark
POLKIT_TYPE_ERROR
polkit_error_get_type
</SECTION>

<SECTION>
<FILE>polkitdetails</FILE>
PolkitDetails
polkit_details_new
polkit_details_lookup
polkit_details_insert
polkit_details_get_keys
<SUBSECTION Standard>
PolkitDetailsClass
POLKIT_DETAILS
POLKIT_IS_DETAILS
POLKIT_TYPE_DETAILS
polkit_details_get_type
POLKIT_DETAILS_CLASS
POLKIT_IS_DETAILS_CLASS
POLKIT_DETAILS_GET_CLASS
</SECTION>

<SECTION>
<FILE>polkitagentsession</FILE>
<TITLE>PolkitAgentSession</TITLE>
PolkitAgentSession
polkit_agent_session_new
polkit_agent_session_initiate
polkit_agent_session_response
polkit_agent_session_cancel
<SUBSECTION Standard>
POLKIT_AGENT_SESSION
POLKIT_AGENT_IS_SESSION
POLKIT_AGENT_TYPE_SESSION
polkit_agent_session_get_type
POLKIT_AGENT_SESSION_CLASS
POLKIT_AGENT_IS_SESSION_CLASS
POLKIT_AGENT_SESSION_GET_CLASS
</SECTION>

<SECTION>
<FILE>polkitagentlistener</FILE>
<TITLE>PolkitAgentListener</TITLE>
PolkitAgentListener
PolkitAgentListenerClass
polkit_agent_listener_initiate_authentication
polkit_agent_listener_initiate_authentication_finish
PolkitAgentRegisterFlags
polkit_agent_listener_register
polkit_agent_listener_register_with_options
polkit_agent_listener_unregister
polkit_agent_register_listener
<SUBSECTION Standard>
POLKIT_AGENT_LISTENER
POLKIT_AGENT_IS_LISTENER
POLKIT_AGENT_TYPE_LISTENER
polkit_agent_listener_get_type
POLKIT_AGENT_LISTENER_CLASS
POLKIT_AGENT_IS_LISTENER_CLASS
POLKIT_AGENT_LISTENER_GET_CLASS
</SECTION>

<SECTION>
<FILE>polkitagenttextlistener</FILE>
<TITLE>PolkitAgentTextListener</TITLE>
PolkitAgentTextListener
polkit_agent_text_listener_new
<SUBSECTION Standard>
POLKIT_AGENT_TEXT_LISTENER
POLKIT_AGENT_IS_TEXT_LISTENER
POLKIT_AGENT_TYPE_TEXT_LISTENER
polkit_agent_text_listener_get_type
POLKIT_AGENT_TEXT_LISTENER_CLASS
POLKIT_AGENT_IS_TEXT_LISTENER_CLASS
POLKIT_AGENT_TEXT_LISTENER_GET_CLASS
</SECTION>

<SECTION>
<FILE>polkittemporaryauthorization</FILE>
<TITLE>PolkitTemporaryAuthorization</TITLE>
PolkitTemporaryAuthorization
polkit_temporary_authorization_get_id
polkit_temporary_authorization_get_action_id
polkit_temporary_authorization_get_subject
polkit_temporary_authorization_get_time_obtained
polkit_temporary_authorization_get_time_expires
<SUBSECTION Standard>
POLKIT_TEMPORARY_AUTHORIZATION
POLKIT_IS_TEMPORARY_AUTHORIZATION
POLKIT_TYPE_TEMPORARY_AUTHORIZATION
polkit_temporary_authorization_get_type
POLKIT_TEMPORARY_AUTHORIZATION_CLASS
POLKIT_IS_TEMPORARY_AUTHORIZATION_CLASS
POLKIT_TEMPORARY_AUTHORIZATION_GET_CLASS
</SECTION>

<SECTION>
<FILE>polkitpermission</FILE>
PolkitPermission
polkit_permission_new
polkit_permission_new_finish
polkit_permission_new_sync
polkit_permission_get_action_id
polkit_permission_get_subject
<SUBSECTION Standard>
PolkitPermissionClass
POLKIT_PERMISSION
POLKIT_IS_PERMISSION
POLKIT_TYPE_PERMISSION
polkit_permission_get_type
POLKIT_PERMISSION_CLASS
POLKIT_IS_PERMISSION_CLASS
POLKIT_PERMISSION_GET_CLASS
</SECTION>
