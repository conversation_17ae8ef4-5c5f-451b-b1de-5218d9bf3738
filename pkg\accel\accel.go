package accel

import (
	"errors"

	oci "github.com/opencontainers/runtime-spec/specs-go"
)

type Accel interface {
	Load(config *oci.Spec) error
	Unload()
	Type() string
}

var NotFoudErr = errors.New("not found acces")

var sandboxAccel map[string]Accel = make(map[string]Accel)

func Register(r Accel) {
	sandboxAccel[r.Type()] = r
}

func GetAccel(name string) (Accel, error) {
	accel, ok := sandboxAccel[name]
	if !ok {
		return nil, NotFoudErr
	}
	return accel, nil
}
