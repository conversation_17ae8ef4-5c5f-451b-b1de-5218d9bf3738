//go:build !linux
// +build !linux

package device

import "errors"

// Major returns the major component of a Linux device number.
func Major(dev uint64) uint32 {
	major := uint32((dev & 0x00000000000fff00) >> 8)
	major |= uint32((dev & 0xfffff00000000000) >> 32)
	return major
}

// Minor returns the minor component of a Linux device number.
func Minor(dev uint64) uint32 {
	minor := uint32((dev & 0x00000000000000ff) >> 0)
	minor |= uint32((dev & 0x00000ffffff00000) >> 12)
	return minor
}

type DeviceNumber struct {
	Major string
	Minor string
}

func GetDeviceNumber(device string) (DeviceNumber, error) {
	return DeviceNumber{}, errors.New("not support")
}

func SetSandBoxDeviceAccel(sanbox, device string) error {
	return errors.New("not support")
}
