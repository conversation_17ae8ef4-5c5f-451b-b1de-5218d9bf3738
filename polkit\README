OVERVIEW
========

polkit is a toolkit for defining and handling authorizations.  It is
used for allowing unprivileged processes to speak to privileged
processes.

RELEASES
========

Releases of polkit are available in compressed tarballs from

 http://www.freedesktop.org/software/polkit/releases/

To verify the authenticity of the compressed tarball, use this command

 $ gpg --verify polkit-$(VERSION).tar.gz.sign polkit-$(VERSION).tar.gz
 gpg: Signature made Sat 10 Mar 2012 03:00:30 PM EST using RSA key ID 3418A891
 gpg: Good signature from "<PERSON> <zeuthe<PERSON>@gmail.com>"
 gpg:                 aka "[jpeg image of size 5237]"

BUGS and DEVELOPMENT
====================

Please report non-security bugs via the freedesktop.org bugzilla at

 https://bugs.freedesktop.org/enter_bug.cgi?product=PolicyKit

SECURITY ISSUES
===============

polkit uses the same mechanism for reporting security issues as dbus,
the most recent copy of instructions can be found in the DBus git
repository:

http://cgit.freedesktop.org/dbus/dbus/tree/HACKING

A copy of the instructions as of 2015-06-04:

If you find a security vulnerability that is not known to the public,
please report it <NAME_EMAIL>
or by reporting a freedesktop.org bug that is marked as
restricted to the "D-BUS security group".
