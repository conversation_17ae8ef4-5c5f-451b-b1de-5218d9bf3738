//go:build linux

// +build: linux

package file

import (
	"errors"
	"syscall"
)

type MountInfo struct {
	Source string
	Target string
	FSType string
	Flags  uintptr
	Data   string
}

func Mount(mi MountInfo) error {
	if mi.Source == "" || mi.Target == "" || mi.FSType == "" {
		return errors.New("source、target or fstype is empty")
	}
	return syscall.Mount(mi.Source, mi.Target, mi.FSType, mi.Flags, mi.Data)
}

func Unmount(path string, flags int) error {
	return syscall.Unmount(path, flags)
}
