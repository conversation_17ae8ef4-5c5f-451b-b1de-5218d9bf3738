package cmd

import (
	"context"
	goflags "flag"
	"os"
	goruntime "runtime"
	"code.alipay.com/antjail/antjail/pkg/librunlc"
	"github.com/spf13/cobra"
	"k8s.io/klog/v2"
)

var containerName = ""
var bundle = ""
var rootless = false
var nocgroup = false

func newRunlcSandBox() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "runlc_startSandbox",
		Short: "internal runcl start sandbox",
		Run: func(cmd *cobra.Command, args []string) {
			if len(args) == 0 {
				os.Exit(1)
			}
			goruntime.LockOSThread()
			defer goruntime.UnlockOSThread()
			librunlc.StartSandBox(context.Background(), bundle, containerName, rootless, nocgroup, args)
		},
	}
	cmd.PersistentFlags().StringVar(&containerName, "containerName", "", "sandbox name")
	cmd.PersistentFlags().StringVar(&bundle, "bundle", "", "bundle dir")
	cmd.PersistentFlags().BoolVarP(&rootless, "rootless", "", false, "rootless mode")
	cmd.PersistentFlags().BoolVarP(&nocgroup, "nocgroup", "", false, "disable cgroup processing")
	fs := goflags.NewFlagSet("", goflags.PanicOnError)
	klog.InitFlags(fs)
	cmd.Flags().AddGoFlagSet(fs)
	return cmd
}
