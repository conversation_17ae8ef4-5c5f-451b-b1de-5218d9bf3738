
NULL =

AUTOMAKE_OPTIONS = 1.7

# The name of the module.
DOC_MODULE=polkit-1

# The top-level SGML file.
DOC_MAIN_SGML_FILE=polkit-1-docs.xml

# Extra options to supply to gtkdoc-scan
SCAN_OPTIONS=

# Directories containing the source code.
# gtk-doc will search all .c and .h files beneath these paths
# for inline comments documenting functions and macros.
# e.g. DOC_SOURCE_DIR=$(top_srcdir)/gtk $(top_srcdir)/gdk
DOC_SOURCE_DIR=$(top_srcdir)/src/polkit $(top_srcdir)/src/polkitagent

# Used for dependencies
HFILE_GLOB=$(top_srcdir)/src/polkit*/*.h
CFILE_GLOB=$(top_srcdir)/src/polkit*/*.c

# Headers to ignore
IGNORE_HFILES= \
	polkitprivate.h \
	polkitagenthelperprivate.h \
	$(NULL)

# CFLAGS and LDFLAGS for compiling scan program. Only needed
# if $(DOC_MODULE).types is non-empty.
AM_CPPFLAGS = \
	$(GLIB_CFLAGS)						\
	-I$(top_srcdir)/src/polkit 				\
	-I$(top_builddir)/src/polkit				\
	-I$(top_srcdir)/src/polkitagent 			\
	-I$(top_builddir)/src/polkitagent			\
	$(NULL)

GTKDOC_LIBS = \
	$(GLIB_LIBS)						\
	$(top_builddir)/src/polkit/libpolkit-gobject-1.la	\
	$(top_builddir)/src/polkitagent/libpolkit-agent-1.la	\
	$(NULL)

# Extra options to supply to gtkdoc-mkdb
MKDB_OPTIONS=--sgml-mode --output-format=xml --name-space=polkit

# Extra options to supply to gtkdoc-mktmpl
MKTMPL_OPTIONS=

# Extra options to supply to gtkdoc-mkhtml - workaround for incorrect check
# in gtk-doc-1.18
MKHTML_OPTIONS=--path=$(srcdir)

# Non-autogenerated SGML files to be included in $(DOC_MAIN_SGML_FILE)
content_files =  			    								\
	overview.xml											\
	../extensiondir.xml			    							\
	../version.xml			    								\
	docbook-interface-org.freedesktop.PolicyKit1.Authority.xml					\
	docbook-interface-org.freedesktop.PolicyKit1.AuthenticationAgent.xml				\
	../man/polkit.xml										\
	../man/polkitd.xml										\
	../man/pkcheck.xml										\
	../man/pkaction.xml										\
	../man/pkexec.xml										\
	../man/pkttyagent.xml										\
	../../COPYING											\
	$(NULL)

# Images to copy into HTML directory
HTML_IMAGES = 						\
	../polkit-architecture.png			\
	../polkit-authentication-agent-example.png	\
	../polkit-authentication-agent-example-wheel.png \
	$(NULL)

# Extra options to supply to gtkdoc-fixref
FIXXREF_OPTIONS=

if ENABLE_GTK_DOC
include $(top_srcdir)/gtk-doc.make
else
CLEANFILES =
endif

CLEANFILES +=   *~				\
		polkit-1-scan.*			\
		polkit-1.args			\
		polkit-1.hierarchy		\
		polkit-1.interfaces		\
		polkit-1.prerequisites		\
		polkit-1.signals		\
		*.bak				\
		polkit-1-decl-list.txt		\
		polkit-1-decl.txt		\
		polkit-1-overrides.txt		\
		polkit-1-undeclared.txt		\
		polkit-1-undocumented.txt	\
		*.stamp				\
		-rf html xml			\
		$(NULL)


-include $(top_srcdir)/git.mk
