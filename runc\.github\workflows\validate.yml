name: validate
on:
  push:
    tags:
      - v*
    branches:
      - main
      - release-*
  pull_request:
env:
  GO_VERSION: 1.20.x
permissions:
  contents: read

jobs:
  keyring:
    runs-on: ubuntu-22.04
    steps:
    - uses: actions/checkout@v3
    - name: check runc.keyring
      run: make validate-keyring

  lint:
    permissions:
      contents: read
      pull-requests: read
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 2
      - uses: actions/setup-go@v4
        with:
          go-version: "${{ env.GO_VERSION }}"
          cache: false # golangci-lint-action does its own caching
      - name: install deps
        run: |
          sudo apt -q update
          sudo apt -q install libseccomp-dev
      - uses: golangci/golangci-lint-action@v3
        with:
          version: v1.51
      # Extra linters, only checking new code from a pull request.
      - name: lint-extra
        if: github.event_name == 'pull_request'
        run: |
          golangci-lint run --config .golangci-extra.yml --new-from-rev=HEAD~1 --out-format=github-actions

  compile-buildtags:
    runs-on: ubuntu-22.04
    env:
      # Don't ignore C warnings. Note that the output of "go env CGO_CFLAGS" by default is "-g -O2", so we keep them.
      CGO_CFLAGS: -g -O2 -Werror
    steps:
      - uses: actions/checkout@v3
      - name: install go
        uses: actions/setup-go@v4
        with:
          go-version: "${{ env.GO_VERSION }}"
      - name: compile with no build tags
        run: make BUILDTAGS=""

  codespell:
    runs-on: ubuntu-22.04
    steps:
    - uses: actions/checkout@v3
    - name: install deps
      # Version of codespell bundled with Ubuntu is way old, so use pip.
      run: pip install codespell
    - name: run codespell
      run: codespell

  shfmt:
    runs-on: ubuntu-22.04
    steps:
    - uses: actions/checkout@v3
    - name: shfmt
      run: make shfmt

  shellcheck:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v3
      - name: install shellcheck
        env:
          VERSION: v0.9.0
          BASEURL: https://github.com/koalaman/shellcheck/releases/download
          SHA256: 7087178d54de6652b404c306233264463cb9e7a9afeb259bb663cc4dbfd64149
        run: |
          mkdir ~/bin
          curl -sSfL --retry 5 $BASEURL/$VERSION/shellcheck-$VERSION.linux.x86_64.tar.xz |
            tar xfJ - -C ~/bin --strip 1 shellcheck-$VERSION/shellcheck
          sha256sum --strict --check - <<<"$SHA256 *$HOME/bin/shellcheck"
          # make sure to remove the old version
          sudo rm -f /usr/bin/shellcheck
          # Add ~/bin to $PATH.
          echo ~/bin >> $GITHUB_PATH
      - uses: lumaxis/shellcheck-problem-matchers@v2
      - name: run
        run: make shellcheck
      - name: check-config.sh
        run : ./script/check-config.sh

  space-at-eol:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - run: rm -fr vendor
      - run: if git -P grep -I -n '\s$'; then echo "^^^ extra whitespace at EOL, please fix"; exit 1; fi

  deps:
    runs-on: ubuntu-22.04
    steps:
    - uses: actions/checkout@v3
    - name: install go
      uses: actions/setup-go@v4
      with:
        go-version: "${{ env.GO_VERSION }}"
    - name: verify deps
      run: make verify-dependencies


  commit:
    permissions:
      contents: read
      pull-requests: read
    runs-on: ubuntu-22.04
    # Only check commits on pull requests.
    if: github.event_name == 'pull_request'
    steps:
      - name: get pr commits
        id: 'get-pr-commits'
        uses: tim-actions/get-pr-commits@v1.3.0
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: check subject line length
        uses: tim-actions/commit-message-checker-with-regex@v0.3.1
        with:
          commits: ${{ steps.get-pr-commits.outputs.commits }}
          pattern: '^.{0,72}(\n.*)*$'
          error: 'Subject too long (max 72)'

  cfmt:
    runs-on: ubuntu-22.04
    steps:
    - name: checkout
      uses: actions/checkout@v3
      with:
        fetch-depth: 0
    - name: install deps
      run: |
        sudo apt -qq update
        sudo apt -qq install indent
    - name: cfmt
      run: |
        make cfmt
        git diff --exit-code


  release:
    runs-on: ubuntu-22.04
    steps:
    - name: checkout
      uses: actions/checkout@v3
      with:
        fetch-depth: 0

    - name: check CHANGELOG.md
      run: make verify-changelog

      # We have to run this under Docker as Ubuntu (host) does not support all
      # the architectures we want to compile test against, and Dockerfile uses
      # Debian (which does).
      #
      # XXX: as currently this is the only job that is using Docker, we are
      # building and using the runcimage locally. In case more jobs running
      # under Docker will emerge, it will be good to have a separate make
      # runcimage job and share its result (the docker image) with whoever
      # needs it.
    - name: build docker image
      run: make runcimage
    - name: make releaseall
      run: make releaseall
    - name: upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: release-${{ github.run_id }}
        path: release/*


  get-images:
    runs-on: ubuntu-22.04
    steps:
    - uses: actions/checkout@v3
      with:
        fetch-depth: 0
    - name: install bashbrew
      env:
        BASEURL: https://github.com/docker-library/bashbrew/releases/download
        VERSION: v0.1.7
        SHA256: 6b71a6fccfb2025d48a2b23324836b5513c29abfd2d16a57b7a2f89bd02fe53a
      run: |
        mkdir ~/bin
        curl -sSfL --retry 5 -o ~/bin/bashbrew \
          $BASEURL/$VERSION/bashbrew-amd64
        sha256sum --strict --check - <<<"$SHA256 *$HOME/bin/bashbrew"
        chmod a+x ~/bin/bashbrew
        # Add ~/bin to $PATH.
        echo ~/bin >> $GITHUB_PATH
    - name: check that get-images.sh is up to date
      run: |
        cd tests/integration
        ./bootstrap-get-images.sh > get-images.sh
        git diff --exit-code
