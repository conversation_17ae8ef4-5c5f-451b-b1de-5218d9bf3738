# Ukrainian translation for polkit.
# Copyright (C) 2015 polkit's COPYRIGHT HOLDER
# This file is distributed under the same license as the polkit package.
#
# <PERSON> <<EMAIL>>, 2015.
msgid ""
msgstr ""
"Project-Id-Version: polkit master\n"
"Report-Msgid-Bugs-To: https://bugs.freedesktop.org/enter_bug.cgi?"
"product=PolicyKit&keywords=I18N+L10N&component=libpolkit\n"
"POT-Creation-Date: 2015-11-15 02:06+0000\n"
"PO-Revision-Date: 2015-11-15 12:21+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Ukrainian <<EMAIL>>\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=4; plural=n==1 ? 3 : n%10==1 && n%100!=11 ? 0 : n%"
"10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"
"X-Generator: Lokalize 1.5\n"

#: ../actions/org.freedesktop.policykit.policy.in.h:1
msgid "Run a program as another user"
msgstr "Виконання програми від імені іншого користувача"

#: ../actions/org.freedesktop.policykit.policy.in.h:2
msgid "Authentication is required to run a program as another user"
msgstr ""
"Для виконання програми від імені іншого користувача слід пройти розпізнавання"

#: ../src/examples/org.freedesktop.policykit.examples.pkexec.policy.in.h:1
msgid "Run the polkit example program Frobnicate"
msgstr "Виконання прикладу програми polkit Frobnicate"

#: ../src/examples/org.freedesktop.policykit.examples.pkexec.policy.in.h:2
msgid ""
"Authentication is required to run the polkit example program Frobnicate "
"(user=$(user), user.gecos=$(user.gecos), user.display=$(user.display), "
"program=$(program), command_line=$(command_line))"
msgstr ""
"Для запуску прикладу програми polkit Frobnicate "
"(user=$(user), user.gecos=$(user.gecos), user.display=$(user.display), "
"program=$(program), command_line=$(command_line)) слід пройти розпізнавання"

#: ../src/programs/pkaction.c:100
msgid "Only output information about ACTION"
msgstr "Вивести дані лише щодо дії ДІЯ"

#: ../src/programs/pkaction.c:100
msgid "ACTION"
msgstr "ДІЯ"

#: ../src/programs/pkaction.c:104
msgid "Output detailed action information"
msgstr "Вивести докладні дані щодо дії"

#: ../src/programs/pkaction.c:108 ../src/programs/pkttyagent.c:61
msgid "Show version"
msgstr "Показати дані щодо версії"

#: ../src/programs/pkaction.c:130
msgid "[--action-id ACTION]"
msgstr "[--action-id ДІЯ]"

#: ../src/programs/pkaction.c:131 ../src/programs/pkttyagent.c:81
#, c-format
msgid ""
"Report bugs to: %s\n"
"%s home page: <%s>"
msgstr ""
"Про вади повідомляйте за такою адресою: %s\n"
"Домашня сторінка %s: <%s>"

#: ../src/programs/pkaction.c:145 ../src/programs/pkcheck.c:491
#: ../src/programs/pkttyagent.c:95
#, c-format
msgid "%s: Unexpected argument `%s'\n"
msgstr "%s: неочікуваний аргумент «%s»\n"

#: ../src/programs/pkcheck.c:35
#, c-format
msgid ""
"Usage:\n"
"  pkcheck [OPTION...]\n"
"\n"
"Help Options:\n"
"  -h, --help                         Show help options\n"
"\n"
"Application Options:\n"
"  -a, --action-id=ACTION             Check authorization to perform ACTION\n"
"  -u, --allow-user-interaction       Interact with the user if necessary\n"
"  -d, --details=KEY VALUE            Add (KEY, VALUE) to information about "
"the action\n"
"  --enable-internal-agent            Use an internal authentication agent if "
"necessary\n"
"  --list-temp                        List temporary authorizations for "
"current session\n"
"  -p, --process=PID[,START_TIME,UID] Check authorization of specified "
"process\n"
"  --revoke-temp                      Revoke all temporary authorizations for "
"current session\n"
"  -s, --system-bus-name=BUS_NAME     Check authorization of owner of "
"BUS_NAME\n"
"  --version                          Show version\n"
"\n"
"Report bugs to: %s\n"
"%s home page: <%s>\n"
msgstr ""
"Користування:\n"
"  pkcheck [ПАРАМЕТР...]\n"
"\n"
"Параметри довідки:\n"
"  -h, --help                         Вивести довідку щодо параметрів\n"
"\n"
"Параметри програми:\n"
"  -a, --action-id=ДІЯ                Перевірити уповноваження щодо виконання "
"дії ДІЯ\n"
"  -u, --allow-user-interaction       Взаємодіяти із користувачем, якщо "
"потрібно\n"
"  -d, --details=КЛЮЧ ЗНАЧЕННЯ        Додати пару (КЛЮЧ, ЗНАЧЕННЯ) до "
"інформації щодо дії\n"
"  --enable-internal-agent            Використати вбудований агент "
"розпізнавання, якщо потрібно\n"
"  --list-temp                        Вивести список тимчасових уповноважень "
"для поточного сеансу\n"
"  -p, --process=PID[,ЧАС_ЗАПУСКУ,UID] Перевірити уповноваження для вказаного "
"процесу\n"
"  --revoke-temp                      Відкликати усі тимчасові уповноваження "
"для поточного сеансу\n"
"  -s, --system-bus-name=НАЗВА_КАНАЛУ Перевірити уповноваження власника "
"НАЗВА_КАНАЛУ\n"
"  --version                          Вивести дані щодо версії\n"
"\n"
"Про вади повідомляйте за такою адресою: %s\n"
"Домашня сторінка %s: <%s>\n"

#: ../src/programs/pkcheck.c:391 ../src/programs/pkcheck.c:424
#: ../src/programs/pkcheck.c:436
#, c-format
msgid "%s: Argument expected after `%s'\n"
msgstr "%s: після «%s» мало бути вказано аргумент'\n"

#: ../src/programs/pkcheck.c:414
#, c-format
msgid "%s: Invalid --process value `%s'\n"
msgstr "%s: некоректне значення --process, «%s»\n"

#: ../src/programs/pkcheck.c:451 ../src/programs/pkcheck.c:460
#, c-format
msgid "%s: Two arguments expected after `--detail'\n"
msgstr "%s: після «--detail» мало бути вказано два аргументи\n"

#: ../src/programs/pkcheck.c:521
#, c-format
msgid "%s: Subject not specified\n"
msgstr "%s: не вказано суб’єкт\n"

#. Translators: message shown when trying to run a program as root. Do not
#. * translate the $(program) fragment - it will be expanded to the path
#. * of the program e.g.  /bin/bash.
#.
#: ../src/programs/pkexec.c:794
msgid "Authentication is needed to run `$(program)' as the super user"
msgstr ""
"Для запуску «$(program)» від імені суперкористувача слід пройти розпізнавання"

#. Translators: message shown when trying to run a program as another user.
#. * Do not translate the $(program) or $(user) fragments - the former will
#. * be expanded to the path of the program e.g. "/bin/bash" and the latter
#. * to the user e.g. "John Doe (johndoe)" or "johndoe".
#.
#: ../src/programs/pkexec.c:804
msgid "Authentication is needed to run `$(program)' as user $(user.display)"
msgstr ""
"Для запуску «$(program)» від імені користувача $(user.display) слід пройти "
"розпізнавання"

#: ../src/programs/pkttyagent.c:44
msgid "Don't replace existing agent if any"
msgstr "Не змінювати наявного агента, якщо такий є"

#: ../src/programs/pkttyagent.c:48
msgid "Close FD when the agent is registered"
msgstr "Закрити дескриптор файла, якщо агент не зареєстровано"

#: ../src/programs/pkttyagent.c:48
msgid "FD"
msgstr "ДФ"

#: ../src/programs/pkttyagent.c:52
msgid "Register the agent for the specified process"
msgstr "Зареєструвати агент для вказаного процесу"

#: ../src/programs/pkttyagent.c:53
msgid "PID[,START_TIME]"
msgstr "PID[,ЧАС_ЗАПУСКУ]"

#: ../src/programs/pkttyagent.c:57
msgid "Register the agent owner of BUS_NAME"
msgstr "Зареєструвати власника агента НАЗВА_КАНАЛУ"

#: ../src/programs/pkttyagent.c:57
msgid "BUS_NAME"
msgstr "НАЗВА_КАНАЛУ"

#: ../src/programs/pkttyagent.c:127
#, c-format
msgid "%s: Invalid process specifier `%s'\n"
msgstr "%s: некоректний специфікатор процесу, «%s»\n"
