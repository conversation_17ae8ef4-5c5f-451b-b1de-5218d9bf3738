# translation of pl.po to Polish
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2010, 2015.
#
msgid ""
msgstr ""
"Project-Id-Version: polkit\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-08-28 15:31+0200\n"
"PO-Revision-Date: 2015-08-28 15:32+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Polish <<EMAIL>>\n"
"Language: pl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 "
"|| n%100>=20) ? 1 : 2);\n"

#: ../actions/org.freedesktop.policykit.policy.in.h:1
msgid "Run a program as another user"
msgstr "Uruchomienie programu jako inny użytkownik"

#: ../actions/org.freedesktop.policykit.policy.in.h:2
msgid "Authentication is required to run a program as another user"
msgstr ""
"Wymagane jest uwierzytelnienie, aby uruchomić program jako inny użytkownik"

#: ../src/examples/org.freedesktop.policykit.examples.pkexec.policy.in.h:1
msgid "Run the polkit example program Frobnicate"
msgstr "Uruchomienie przykładowego programu polkit „Frobnicate”"

#: ../src/examples/org.freedesktop.policykit.examples.pkexec.policy.in.h:2
msgid ""
"Authentication is required to run the polkit example program Frobnicate "
"(user=$(user), user.gecos=$(user.gecos), user.display=$(user.display), "
"program=$(program), command_line=$(command_line))"
msgstr ""
"Wymagane jest uwierzytelnienie, aby uruchomić przykładowy program polkit "
"„Frobnicate” (user=$(user), user.gecos=$(user.gecos), user.display=$(user."
"display), program=$(program), command_line=$(command_line))"

#: ../src/programs/pkaction.c:100
msgid "Only output information about ACTION"
msgstr "Wyświetla tylko informacje o DZIAŁANIU"

#: ../src/programs/pkaction.c:100
msgid "ACTION"
msgstr "DZIAŁANIE"

#: ../src/programs/pkaction.c:104
msgid "Output detailed action information"
msgstr "Wyświetla szczegółowe informacje o działaniu"

#: ../src/programs/pkaction.c:108 ../src/programs/pkttyagent.c:61
msgid "Show version"
msgstr "Wyświetla wersję"

#: ../src/programs/pkaction.c:130
msgid "[--action-id ACTION]"
msgstr "[--action-id DZIAŁANIE]"

#: ../src/programs/pkaction.c:131 ../src/programs/pkttyagent.c:81
#, c-format
msgid ""
"Report bugs to: %s\n"
"%s home page: <%s>"
msgstr ""
"Prosimy zgłaszać błędy na (w języku angielskim): %s\n"
"Strona domowa programu %s: <%s>"

#: ../src/programs/pkaction.c:145 ../src/programs/pkcheck.c:491
#: ../src/programs/pkttyagent.c:95
#, c-format
msgid "%s: Unexpected argument `%s'\n"
msgstr "%s: nieoczekiwany parametr „%s”\n"

#: ../src/programs/pkcheck.c:35
#, c-format
msgid ""
"Usage:\n"
"  pkcheck [OPTION...]\n"
"\n"
"Help Options:\n"
"  -h, --help                         Show help options\n"
"\n"
"Application Options:\n"
"  -a, --action-id=ACTION             Check authorization to perform ACTION\n"
"  -u, --allow-user-interaction       Interact with the user if necessary\n"
"  -d, --details=KEY VALUE            Add (KEY, VALUE) to information about "
"the action\n"
"  --enable-internal-agent            Use an internal authentication agent if "
"necessary\n"
"  --list-temp                        List temporary authorizations for "
"current session\n"
"  -p, --process=PID[,START_TIME,UID] Check authorization of specified "
"process\n"
"  --revoke-temp                      Revoke all temporary authorizations for "
"current session\n"
"  -s, --system-bus-name=BUS_NAME     Check authorization of owner of "
"BUS_NAME\n"
"  --version                          Show version\n"
"\n"
"Report bugs to: %s\n"
"%s home page: <%s>\n"
msgstr ""
"Użycie:\n"
"  pkcheck [OPCJA…]\n"
"\n"
"Opcje pomocy:\n"
"  -h, --help                             Wyświetla opcje pomocy\n"
"\n"
"Opcje programu:\n"
"  -a, --action-id=DZIAŁANIE              Sprawdza upoważnienie do wykonania\n"
"                                         DZIAŁANIA\n"
"  -u, --allow-user-interaction           Pyta użytkownika, jeśli trzeba\n"
"  -d, --details=KLUCZ WARTOŚĆ            Dodaje (KLUCZ, WARTOŚĆ) do\n"
"                                         informacji o działaniu\n"
"  --enable-internal-agent                Używa wewnętrznego agenta\n"
"                                         uwierzytelniania, jeśli trzeba\n"
"  --list-temp                            Wyświetla listę tymczasowych\n"
"                                         upoważnień dla bieżącej sesji\n"
"  -p, --process=PID[,CZAS_STARTOWY,UID]  Sprawdza upoważnienie podanego\n"
"                                         procesu\n"
"  --revoke-temp                          Unieważnia wszystkie tymczasowe\n"
"                                         upoważnienia dla bieżącej sesji\n"
"  -s, --system-bus-name=NAZWA_MAGISTRALI Sprawdza upoważnienie właściciela\n"
"                                         NAZWY_MAGISTRALI\n"
"  --version                              Wyświetla wersję\n"
"\n"
"Prosimy zgłaszać błędy na (w języku angielskim): %s\n"
"Strona domowa programu %s: <%s>\n"

#: ../src/programs/pkcheck.c:391 ../src/programs/pkcheck.c:424
#: ../src/programs/pkcheck.c:436
#, c-format
msgid "%s: Argument expected after `%s'\n"
msgstr "%s: oczekiwano parametru po opcji „%s”\n"

#: ../src/programs/pkcheck.c:414
#, c-format
msgid "%s: Invalid --process value `%s'\n"
msgstr "%s: nieprawidłowa wartość „%s” opcji --process\n"

#: ../src/programs/pkcheck.c:451 ../src/programs/pkcheck.c:460
#, c-format
msgid "%s: Two arguments expected after `--detail'\n"
msgstr "%s: oczekiwano dwóch parametrów po opcji „--detail”\n"

#: ../src/programs/pkcheck.c:521
#, c-format
msgid "%s: Subject not specified\n"
msgstr "%s: nie podano tematu\n"

#. Translators: message shown when trying to run a program as root. Do not
#. * translate the $(program) fragment - it will be expanded to the path
#. * of the program e.g.  /bin/bash.
#.
#: ../src/programs/pkexec.c:794
msgid "Authentication is needed to run `$(program)' as the super user"
msgstr ""
"Wymagane jest uwierzytelnienie, aby uruchomić program „$(program)” jako "
"superużytkownik"

#. Translators: message shown when trying to run a program as another user.
#. * Do not translate the $(program) or $(user) fragments - the former will
#. * be expanded to the path of the program e.g. "/bin/bash" and the latter
#. * to the user e.g. "John Doe (johndoe)" or "johndoe".
#.
#: ../src/programs/pkexec.c:804
msgid "Authentication is needed to run `$(program)' as user $(user.display)"
msgstr ""
"Wymagane jest uwierzytelnienie, aby uruchomić program „$(program)” jako "
"użytkownik $(user.display)"

#: ../src/programs/pkttyagent.c:44
msgid "Don't replace existing agent if any"
msgstr "Bez zastępowania istniejących agentów, jeśli jakieś są"

#: ../src/programs/pkttyagent.c:48
msgid "Close FD when the agent is registered"
msgstr "Zamyka deskryptor pliku po zarejestrowaniu agenta"

#: ../src/programs/pkttyagent.c:48
msgid "FD"
msgstr "DP"

#: ../src/programs/pkttyagent.c:52
msgid "Register the agent for the specified process"
msgstr "Rejestruje agenta dla podanego procesu"

#: ../src/programs/pkttyagent.c:53
msgid "PID[,START_TIME]"
msgstr "PID[,CZAS_STARTOWY]"

#: ../src/programs/pkttyagent.c:57
msgid "Register the agent owner of BUS_NAME"
msgstr "Rejestruje właściciela agenta NAZWY_MAGISTRALI"

#: ../src/programs/pkttyagent.c:57
msgid "BUS_NAME"
msgstr "NAZWA_MAGISTRALI"

#: ../src/programs/pkttyagent.c:127
#, c-format
msgid "%s: Invalid process specifier `%s'\n"
msgstr "%s: nieprawidłowe określenie procesu „%s”\n"
