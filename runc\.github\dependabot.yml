# Please see the documentation for all configuration options:
# https://help.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  # Dependencies listed in go.mod
  - package-ecosystem: "gomod"
    directory: "/" # Location of package manifests
    schedule:
      interval: "daily"
    ignore:
      # a regression in v1.22.2, see https://github.com/urfave/cli/issues/1092
      - dependency-name: "github.com/urfave/cli"

  # Dependencies listed in .github/workflows/*.yml
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "daily"

  # Dependencies listed in Dockerfile
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "daily"
