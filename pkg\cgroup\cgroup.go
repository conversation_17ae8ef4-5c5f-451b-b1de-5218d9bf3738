package cgroup

import (
	"fmt"
	"os"
	"strings"
	"bufio"
	"path"
	"io/ioutil"
	"path/filepath"
	"strconv"
	"math/rand"
	"time"

	"k8s.io/klog/v2"
)

const CgroupV1Root = "/sys/fs/cgroup"

func currentCgroup(ctrl string) (string, error) {
	const path = "/proc/self/cgroup"
	file, err := os.Open(path)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// Per proc(5) -> cgroups(7):
	// Each line of /proc/self/cgroups describes a cgroup hierarchy.
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		// Each line consists of 3 colon-separated fields. Find the line for
		// which the second field (controller-list, a comma-separated list of
		// cgroup controllers) contains ctrl.
		line := scanner.Text()
		const nrfields = 3
		fields := strings.Split(line, ":")
		if len(fields) != nrfields {
			return "", fmt.Errorf("failed to parse %s: line %q: got %d fields, wanted %d", path, line, len(fields), nrfields)
		}
		for _, controller := range strings.Split(fields[1], ",") {
			if controller == ctrl {
				return fields[2], nil
			}
		}
	}
	return "", fmt.Errorf("not a member of a cgroup hierarchy for controller %s", ctrl)
}


func CurrentCgroupDirectory(ctrl string) (string, error) {
	root, err := cgroupRootDirectory(ctrl)
	if err != nil {
		return "", err
	}
	cg, err := currentCgroup(ctrl)
	if err != nil {
		return "", err
	}
	return path.Join(root, cg), nil
}


func writeFile(path string, data []byte, perm os.FileMode) error {
	f, err := os.OpenFile(path, os.O_WRONLY|os.O_TRUNC, perm)
	if err != nil {
		return err
	}
	defer f.Close()

	_, err = f.Write(data)
	return err
}


func fillFromAncestor(path string) (string, error) {
	out, err := ioutil.ReadFile(path)
	if err != nil {
		return "", err
	}
	val := strings.TrimSpace(string(out))
	if val != "" {
		// File is set, stop here.
		return val, nil
	}

	// File is not set, recurse to parent and then set here.
	name := filepath.Base(path)
	parent := filepath.Dir(filepath.Dir(path))
	val, err = fillFromAncestor(filepath.Join(parent, name))
	if err != nil {
		return "", err
	}

	if err := writeFile(path, []byte(val), 0700); err != nil {
		return "", nil
	}
	return val, nil
}


func countCpuset(cpuset string) ([]int, error) {
	var cpuSets []int
	for _, p := range strings.Split(cpuset, ",") {
		interval := strings.Split(p, "-")
		switch len(interval) {
		case 1:
			if cpu, err := strconv.Atoi(interval[0]); err == nil {
				cpuSets = append(cpuSets, cpu)
			} else {
				return cpuSets, err
			}

		case 2:
			start, err := strconv.Atoi(interval[0])
			if err != nil {
				return cpuSets, err
			}
			end, err := strconv.Atoi(interval[1])
			if err != nil {
				return cpuSets, err
			}
			if start < 0 || end < 0 || start > end {
				return cpuSets, fmt.Errorf("invalid cpuset: %q", p)
			}
			for i := start ; i <= end; i++ {
				cpuSets = append(cpuSets, i)	
			}

		default:
			return cpuSets, fmt.Errorf("invalid cpuset: %q", p)
		}
	}
	return cpuSets, nil
}


func getValue(path, name string) (string, error) {
	fullpath := filepath.Join(path, name)
	out, err := ioutil.ReadFile(fullpath)
	if err != nil {
		return "", err
	}
	return string(out), nil
}


func setValue(path, name, data string) error {
	fullpath := filepath.Join(path, name)
	return writeFile(fullpath, []byte(data), 0700)
}

/*
func getInt(path, name string) (int, error) {
	s, err := getValue(path, name)
	if err != nil {
		return 0, err
	}
	return strconv.Atoi(strings.TrimSpace(s))
}

*/

func ChooseNRandomNumbers(slice []int, n int) []int {
	rand.Seed(time.Now().UnixNano())
	if n >= len(slice) {
		// 如果请求的数字数量多于切片中元素的数量，则返回切片的副本。
		// 切片是引用类型，所以用copy做一个副本
		result := make([]int, len(slice))
		copy(result, slice)
		return result
	}
	
	// 打乱切片的顺序
	rand.Shuffle(len(slice), func(i, j int) {
		slice[i], slice[j] = slice[j], slice[i]
	})
	
	// 返回切片的前n个元素
	return slice[:n]
}



func SetMaxCPUNumber(sandboxName string, maxCPU int, pid int) error {
	klog.V(4).Info("SetMaxCPUNumber ...")
	cpuCgroup, err := CurrentCgroupDirectory("cpuset")
	if err != nil {
		klog.V(4).Infof("get currentCgroupDirectory err:%s", err.Error())
		return err
	}
	newPath := filepath.Join(cpuCgroup, sandboxName)
	if err := os.MkdirAll(newPath, 0755); err != nil {
		klog.V(4).Infof("mkdir error:%s", err.Error())
		return err
	}

	if _, err := fillFromAncestor(filepath.Join(newPath, "cpuset.cpus")); err != nil {
		klog.V(4).Infof("fillFromAncestor error:%s", err.Error())
		return err
	}

	if _, err := fillFromAncestor(filepath.Join(newPath, "cpuset.mems")); err != nil {
		klog.V(4).Infof("fillFromAncestor error:%s", err.Error())
		return err
	}
	cpuset, err := getValue(newPath, "cpuset.cpus")
	if err != nil {
		klog.V(4).Infof("get cpuset.cpus error, path:%s, error:%s", newPath, err.Error())
		return err
	}
	cpuSets, err := countCpuset(strings.TrimSpace(cpuset))
	if err != nil {
		klog.V(4).Infof("countCputset error:%s", err.Error())
		return err
	}
	newCpusets := ChooseNRandomNumbers(cpuSets, maxCPU)
	var newData string
	for _, c := range newCpusets {
		newData = newData + strconv.Itoa(c) + "," 	
	}
	newData = newData[:len(newData)-1]
	if err := setValue(newPath, "cpuset.cpus", newData); err != nil {
		klog.V(4).Infof("set cpuset.cpus error, path:%s, err: %s",newPath, err.Error())
		return err
	}
	if err := setValue(newPath, "cgroup.procs", strconv.Itoa(pid)); err != nil {
		klog.V(4).Infof("set cgroup.procs error, path:%s, err: %s", newPath, err.Error())
		return err
	}
	klog.V(4).Info("SetMaxCPUNumber over...")
	return nil	
		
}

func SetMaxMemory(sandboxName string, maxMemory uint64, pid int) error {
	klog.V(4).Info("SetMaxMemory ...")
	memCgroup, err := CurrentCgroupDirectory("memory")
	if err != nil {
		klog.V(4).Infof("get currentCgroupDirectory err: %s", err.Error())
		return err
	}
	newPath := filepath.Join(memCgroup, sandboxName)
	if err := os.MkdirAll(newPath, 0755); err != nil {
		klog.V(4).Infof("mkdir err:%s", err.Error())
		return err
	}
	var currLimStr string

	if currLimStr, err = getValue(newPath, "memory.limit_in_bytes"); err != nil {
		klog.V(4).Infof("getValue for memory.limit_in_bytes error:%", err.Error())
		return err
	}

	var currLim uint64
	if currLim, err = strconv.ParseUint(strings.TrimSpace(currLimStr), 10, 64); err != nil {
		klog.V(4).Infof("parseUnit for mmeory.limit_in_bytes error:%s", err.Error())
		return err
	}
	if currLim > maxMemory {
		if err := setValue(newPath, "memory.limit_in_bytes", strconv.FormatInt(int64(maxMemory), 10)); err != nil {
			klog.V(4).Infof("set value memory.limit_in_bytes error, path:%s, err:%s", newPath, err.Error())
		}
	}
	if err := setValue(newPath, "cgroup.procs", strconv.Itoa(pid)); err != nil {
		klog.V(4).Infof("setValue cgroup.procs error, path:%s, err:%s", newPath, err.Error())
	}
	klog.V(4).Info("SetMaxMemory end...")
	return nil
	

}

func MovePidToCgroup(path string, pid int) error {
	if err := setValue(path, "cgroup.procs", strconv.Itoa(pid)); err != nil {
		klog.V(4).Infof("setValue cgroup.procs error, path:%s, err:%s", path, err.Error())
	}
	return nil
}

/*
func main() {
	SetMaxCPUNumber("abc", 4, os.Getpid())
	SetMaxMemory("abc", 5*1024, os.Getpid())
	//time.Sleep(20*time.Second)
	for i := 1; i <= 5; i++ {
		go forever(i) // 使用 go 关键字启动协程
	}

	time.Sleep(60*time.Second)
	//cgroups, _ := currentCgroupDirectory("cpu")
//	fmt.Println(cgroups)
}*/


func cgroupRootDirectory(ctrl string) (string, error) {
	const path = "/proc/self/mounts"
	file, err := os.Open(path)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// Per proc(5) -> fstab(5):
	// Each line of /proc/self/mounts describes a mount.
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		// Each line consists of 6 space-separated fields. Find the line for
		// which the third field (fs_vfstype) is cgroup, and the fourth field
		// (fs_mntops, a comma-separated list of mount options) contains
		// ctrl.
		var spec, file, vfstype, mntopts, freq, passno string
		const nrfields = 6
		line := scanner.Text()
		n, err := fmt.Sscan(line, &spec, &file, &vfstype, &mntopts, &freq, &passno)
		if err != nil {
			return "", fmt.Errorf("failed to parse %s: %v", path, err)
		}
		if n != nrfields {
			return "", fmt.Errorf("failed to parse %s: line %q: got %d fields, wanted %d", path, line, n, nrfields)
		}
		if vfstype != "cgroup" {
			continue
		}
		for _, mntopt := range strings.Split(mntopts, ",") {
			if mntopt == ctrl {
				return file, nil
			}
		}
	}
	return "", fmt.Errorf("no cgroup hierarchy mounted for controller %s", ctrl)
}

