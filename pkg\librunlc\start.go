package librunlc

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/fs"
	"io/ioutil"
	"os"
	"os/exec"
	"path/filepath"
	//goruntime "runtime"
	"sort"
	"strings"
	"syscall"

	"code.alipay.com/antjail/antjail/pkg/runtime"
	fileUtil "code.alipay.com/antjail/antjail/pkg/util/file"
	"github.com/opencontainers/runc/libcontainer/configs"
	"github.com/opencontainers/runc/libcontainer/seccomp"
	"github.com/opencontainers/runtime-spec/specs-go"
	"github.com/sirupsen/logrus"
	"github.com/syndtr/gocapability/capability"
	"golang.org/x/sys/unix"
	"k8s.io/klog/v2"
)

var capabilityMap = make(map[string]capability.Cap, capability.CAP_LAST_CAP+1)

func init() {
	for _, c := range capability.List() {
		if c > capability.CAP_LAST_CAP {
			continue
		}
		capabilityMap["CAP_"+strings.ToUpper(c.String())] = c
	}
}

func StartSandBox(ctx context.Context, bundle, name string, rootless bool, nocgroup bool, flags []string) {
	configFile := filepath.Join(bundle, "runlc_config.json")
	configContent, err := os.ReadFile(configFile)
	if err != nil {
		klog.Fatalf("read sandbox %s runlc config error: %s", name, err.Error())
	}
	config := &runtime.Config{}
	if err := json.Unmarshal(configContent, config); err != nil {
		klog.Fatalf("unmarshal sandbox %s config file error: %s", name, err.Error())
	}
	if len(flags) == 0 {
		klog.Fatal("not found user command")
	}

	if !rootless && pidNamespeceCheck() {
		if err := mountDefaultFSWithForkInPidNamesapce(); err != nil {
			klog.Fatal(err)
		}
	}

	/*
        goruntime.LockOSThread()
	defer goruntime.UnlockOSThread()
	*/
        if err := syscall.Unshare(syscall.CLONE_NEWNS); err != nil {
                klog.Fatalf("unshare new mount namespace error, detail: %s", err.Error())
        }


        syscall.Mount("none", "/", "", syscall.MS_REC|syscall.MS_SLAVE, "")

	applyExecve(config)
	applyRootfs(bundle, config, rootless)

	if *config.SecurityConfig.System.Root.Readonly {
		MakeRootfsRdonly(config)	
	}

	applyReadOnlyPath(config)
	applyWriteablePath(config)
	applyWriteableNoexecPath(config)
	//applyReadOnlyPath(config)
	applyMaskPath(config)
	os.Chdir(config.SecurityConfig.System.Cwd)
	arg0 := flags[0]
	arg0Path, err := exec.LookPath(arg0)
	if err != nil {
		klog.Fatalf("not found %s, error:%s", arg0, err.Error())
	}

	applySeccomp(config)
	applyRunlcCap(config)
	//applySeccomp(config)
	applyUserAndGroup()

	if err := unix.Exec(arg0Path, flags, os.Environ()); err != nil {
		klog.Fatalf("runlc_startSandbox exec %s error: %s", strings.Join(flags, ","), err.Error())
	}
}


func pivotRoot(root string) error {
	if err := os.Chdir(root); err != nil {
		return fmt.Errorf("error changing working directory: %v", err)
	}
	// pivot_root(new_root, put_old) moves the root filesystem (old_root)
	// of the calling process to the directory put_old and makes new_root
	// the new root filesystem of the calling process.
	//
	// pivot_root(".", ".") makes a mount of the working directory the new
	// root filesystem, so it will be moved in "/" and then the old_root
	// will be moved to "/" too. The parent mount of the old_root will be
	// new_root, so after umounting the old_root, we will see only
	// the new_root in "/".
	if err := unix.PivotRoot(".", "."); err != nil {
		return fmt.Errorf("pivot_root failed, make sure that the root mount has a parent: %v", err)
	}

	if err := unix.Unmount(".", unix.MNT_DETACH); err != nil {
		return fmt.Errorf("error umounting the old root file system: %v", err)
	}
	return nil
}


func getExtraMountFlags(path string) uintptr {
	var ret uintptr
        content, err := ioutil.ReadFile("/proc/self/mountinfo")
        if err != nil {
		klog.Fatalf("read /proc/mounts error:%s", err.Error())
		return ret
        }

        lines := strings.Split(string(content), "\n")

        for _, line := range lines {
                line = strings.TrimSpace(line)
                if line == "" {
                        continue
                }
                fields := strings.Fields(line)
		if path == fields[4] {
			if strings.Contains(fields[5], "nosuid") {
				ret |= syscall.MS_NOSUID
			}
			if strings.Contains(fields[5], "noexec") {
				ret |= syscall.MS_NOEXEC
			}
			if strings.Contains(fields[5], "nodev") {
				ret |= syscall.MS_NODEV
			}
			if strings.Contains(fields[5], "ro") {
				ret |= syscall.MS_RDONLY
			}

		}
                //fmt.Printf("Device: %s, MountPoint: %s, FileSystemType: %s, Options: %s\n", fields[0], fields[1], fields[2], strings.Join(fields[3:], ", "))
        }
	return ret
}

/*
func printMounts() {
	content, err := ioutil.ReadFile("/proc/self/mountinfo")
	if err != nil {
		klog.Fatalf("读取文件 /proc/mounts 出错: %v", err)
	}

	fmt.Println("文件 /proc/mounts 的内容:")
	fmt.Println(string(content))
}*/

func displayFileInfo(path string) {
    fileInfo, err := os.Stat(path)
    if err != nil {
        klog.Fatalf("无法获取文件信息: %v\n", err)
    }

    // 获取文件模式
    fileMode := fileInfo.Mode()

    // 转换为 *syscall.Stat_t 以获取 UID 和 GID
    stat, ok := fileInfo.Sys().(*syscall.Stat_t)
    if !ok {
        klog.Fatalf("无法获取文件 %s 的 uid 和 gid\n", path)
    }

    fmt.Printf("路径: %s\n", path)
    fmt.Printf("权限: %s\n", fileMode.String())
    fmt.Printf("权限（八进制）: %o\n", fileMode.Perm())
    fmt.Printf("UID: %d\n", stat.Uid)
    fmt.Printf("GID: %d\n", stat.Gid)
    fmt.Printf("是否为目录: %t\n", fileMode.IsDir())
    fmt.Printf("是否为普通文件: %t\n", fileMode.IsRegular())
}

func applyRootfs(bundle string, config *runtime.Config, rootless bool) {
	paths := config.SecurityConfig.System.Root.Paths

	rootfsPath := filepath.Join(bundle, "rootfs")
	if err := os.Mkdir(rootfsPath, fs.ModePerm); err != nil && !errors.Is(err, os.ErrExist) {
		klog.Fatalf("create run dir %s error, detail: %s", rootfsPath, err.Error())
		return
	}
	fileUtil.CopyFilePermission("/", rootfsPath)
	if len(paths) == 0 {
                klog.V(4).Info("preapre Rootfs from path ...")
                if err := syscall.Mount(config.SecurityConfig.System.Root.Path, rootfsPath, "none", syscall.MS_BIND | syscall.MS_REC, ""); err != nil {
                        klog.Fatalf("mount root path %s to %s error", config.SecurityConfig.System.Root.Path, rootfsPath)
                        return
                }
		if err := syscall.Mount(rootfsPath, rootfsPath, "none", syscall.MS_BIND | syscall.MS_REC, ""); err != nil {
			klog.Fatalf("bind mount rootfs error:%s", err.Error())
		}

		if err := pivotRoot(rootfsPath); err != nil {
			klog.Fatalf("failed to change the root file system: %v", err)
		}
		if err := os.Chdir("/"); err != nil {
			klog.Fatalf("failed to change working directory")
		}
		return
	}

	for _, path := range paths {
		fileInfo, err := os.Lstat(path)
		if err != nil {
			klog.Fatalf("Error retrieving file information:%s", err.Error())
			return
		}
		mode := fileInfo.Mode()
		newPath := rootfsPath + path
		klog.V(4).Info("bind mount %s to %s", path, newPath)
		switch {
		case mode.IsRegular():
			klog.V(4).Infof("bind mount file %s to %s, origin size=%d", path, newPath, fileInfo.Size())
                        dirPath := filepath.Dir(newPath)
                        if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		//		srcDir := filepath.Dir(path)
				os.MkdirAll(dirPath, fs.ModePerm)
		//		fileUtil.CopyFilePermissionRecursive(srcDir, rootfsPath)
                        }
			_, err := os.Stat(newPath) 
			if err == nil {
				klog.V(4).Infof("file %s exist, ignore create and bind mount", newPath)
				break
			}
			if _, err := os.Create(newPath); err != nil {
				klog.Fatalf("create %s error:%s", newPath, err.Error())
				return
			}
		//	fileUtil.CopyFilePermission(path, newPath)
			if err := syscall.Mount(path, newPath, "none", syscall.MS_BIND, ""); err != nil {
				klog.Fatalf("mount %s to  %s error: %s", path, newPath, err.Error())
			}
			fi1, _ := os.Lstat(newPath)
			klog.V(4).Infof("after mount path=%s, size = %d", newPath, fi1.Size())
		case mode.IsDir():
			if err := os.MkdirAll(newPath, fs.ModePerm); err != nil && !errors.Is(err, os.ErrExist) {
				klog.Fatalf("create run dir %s error, detail: %s", newPath, err.Error())
			}
		//	fileUtil.CopyFilePermissionRecursive(path, rootfsPath)
                        if err := syscall.Mount(path, newPath, "none", syscall.MS_BIND|syscall.MS_REC, ""); err != nil {
                                klog.Fatalf("mount %s to  %s error: %s", path, newPath, err.Error())
                        }

			if err := syscall.Mount(newPath, newPath, "none", syscall.MS_BIND | syscall.MS_REC, ""); err != nil {
				klog.Fatalf("mount %s  mount ponit error %s", newPath, err.Error())
			}

			//printMounts()
			if err := syscall.Mount(newPath, newPath, "none", syscall.MS_BIND | syscall.MS_REC | syscall.MS_REMOUNT | getExtraMountFlags(newPath) , ""); err != nil {
				klog.Fatalf("mount %s to readonly error: %s", newPath, err.Error())
			}

		case mode&os.ModeSymlink != 0:
			resolvedPath, err := os.Readlink(path)
			if err != nil {
				klog.Fatalf("readlink %s error: %s", path, err.Error())
			}
                        dirPath := filepath.Dir(newPath)
                        if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		//		srcDir := filepath.Dir(path)
                                os.MkdirAll(dirPath, fs.ModePerm)
		//		fileUtil.CopyFilePermissionRecursive(srcDir, rootfsPath)
                        }
			if err := os.Symlink(resolvedPath, newPath); err != nil {
				klog.Fatal("create link file % error: %s", path, err.Error())
			}
		default:
			dirPath := filepath.Dir(newPath)
			if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		//		srcDir := filepath.Dir(path)
				os.MkdirAll(dirPath, fs.ModePerm)
		//		fileUtil.CopyFilePermissionRecursive(srcDir, rootfsPath)
			}

                        _, err := os.Stat(newPath)
                        if err == nil {
                                klog.V(4).Infof("file %s exist, ignore create and bind mount", newPath)
                                break
                        }
                        file, err := os.Create(newPath)
		//	fileUtil.CopyFilePermission(path, newPath)
                        if err != nil {
                                klog.Fatalf("Error creating file: %s", err.Error())
                                return
                        }
                        defer file.Close()
                        if err := syscall.Mount(path, newPath, "none", syscall.MS_BIND|syscall.MS_REC, ""); err != nil {
                                klog.Fatalf("mount %s to  %s error: %s", path, newPath, err.Error())
                        }
	//		klog.Fatalf("% is of a different type", path)
		}
	}

	//create default /proc dir
	procPath := filepath.Join(rootfsPath, "proc")
	if err := os.Mkdir(procPath, fs.ModePerm); err != nil && errors.Is(err, os.ErrExist) {
		klog.Fatalf("create proc dir % error, detail: %s", procPath, err.Error())
	}
	if err := syscall.Mount("/proc", procPath, "none", syscall.MS_BIND | syscall.MS_REC, ""); err != nil {
		klog.Fatalf("mount rootfs proc error, detail: %s", err.Error())
	}

	/*
	devPath := filepath.Join(rootfsPath, "dev")
	if err := os.Mkdir(devPath, fs.ModePerm); err != nil && errors.Is(err, os.ErrExist) {
		klog.Fatalf("create dev dir %s error, detail: %s", devPath, err.Error())
	}
	
	if err := syscall.Mount("/dev", devPath, "none", syscall.MS_BIND | syscall.MS_REC, ""); err != nil {
		klog.Fatalf("mount rootfs dev error, detail: %s", err.Error())
	}*/

	if err := syscall.Mount(rootfsPath, rootfsPath, "none", syscall.MS_BIND | syscall.MS_REC, ""); err != nil {
		klog.Fatalf("bind mount rootfs error:%s", err.Error())
	}

	if err := pivotRoot(rootfsPath); err != nil {
		klog.Fatalf("failed to change the root file system: %v", err)
	}
	if err := os.Chdir("/"); err != nil {
		klog.Fatalf("failed to change working directory")
	}
	klog.V(4).Info("setup fs successful")
}

func MakeRootfsRdonly(config *runtime.Config) {
	klog.V(4).Info("MakeRootfsRdonly")
	paths := config.SecurityConfig.System.Root.Paths
	// Here we don't distinguish path/paths as both of them need to remount to read only
	if err := syscall.Mount("/", "/", "none", syscall.MS_REMOUNT|syscall.MS_BIND|syscall.MS_RDONLY|syscall.MS_SLAVE | getExtraMountFlags("/"), ""); err != nil {
		klog.Fatalf("remount root directory to readonly error:%s", err.Error())
	}

	for _, path := range paths {
		fileInfo, err := os.Lstat(path)
		if err != nil {
			klog.Fatalf("error retrieving file information: %s", err.Error())
		}
		mode := fileInfo.Mode()
		if mode.IsRegular() || mode.IsDir() {
			klog.V(4).Infof("bind mount %s path to readonly", path)
			klog.V(4).Infof("file size before bind mount=%d", fileInfo.Size())
			if err := syscall.Mount(path, path, "none", syscall.MS_BIND | syscall.MS_SLAVE | syscall.MS_REC, ""); err != nil {
				klog.Fatalf("error bind mount %s, detail: %s", path, err.Error())
			}
			fi, _ := os.Lstat(path)
			klog.V(4).Infof("file size after bind mount = %d", fi.Size())
			if err := syscall.Mount(path, path, "none", syscall.MS_REMOUNT | syscall.MS_BIND | syscall.MS_RDONLY  | syscall.MS_SLAVE | syscall.MS_REC | getExtraMountFlags(path), ""); err != nil {
				klog.Fatalf("error remount %s to readonly error: %s", path, err.Error())
			}
			fi1, _ := os.Lstat(path)
			klog.V(4).Infof("fize size faster remoutn = %d", fi1.Size())
			klog.V(4).Info("make readonly %s ok ", path)
		}
	}
}

func fileInDirs(file string, dirs []string) bool {
	dirPath := filepath.Dir(file)
	for _, dir := range dirs {
		if dirPath == dir || file == dir {
			return true
		}
	}
	return false
}
func applyExecve(config *runtime.Config) {
	if config.SecurityConfig.Security.Execve.Mode == "whitelist" {
		for _, path := range config.SecurityConfig.Security.Execve.Paths {
			rootfsPaths := config.SecurityConfig.System.Root.Paths
			if !fileInDirs(path, rootfsPaths) {
				config.SecurityConfig.System.Root.Paths = append(config.SecurityConfig.System.Root.Paths, path)
			}

		}
	}
	if config.SecurityConfig.Security.Execve.Mode == "blacklist" {
		for _, path := range config.SecurityConfig.Security.Execve.Paths {
			if !fileInDirs(path, config.OCIConfig.Linux.MaskedPaths) {
				config.OCIConfig.Linux.MaskedPaths = append(config.OCIConfig.Linux.MaskedPaths, path)
			}
		}
	}
}

func applyWriteablePath(config *runtime.Config) {
	for _, path := range config.SecurityConfig.Security.File.WritablePaths {
		klog.V(4).Infof("apply %s to writable", path)
		if err := syscall.Mount(path, path, "none", syscall.MS_SLAVE|syscall.MS_BIND | syscall.MS_REC, ""); err != nil {
			klog.V(4).Infof("mount %s as writable error, detail: %s", path, err.Error())
		}
		if err := syscall.Mount(path, path, "none", syscall.MS_SLAVE|syscall.MS_BIND| syscall.MS_REC | syscall.MS_REMOUNT | getExtraMountFlags(path) ^ syscall.MS_RDONLY, ""); err != nil {
			klog.V(4).Infof("remount %s as writable error, detail: %s", path, err.Error())
		}
	}
}

func applyWriteableNoexecPath(config *runtime.Config) {
	for _, path := range config.SecurityConfig.Security.File.WritableNoexecPaths {
                if err := syscall.Mount(path, path, "none", syscall.MS_SLAVE|syscall.MS_BIND | syscall.MS_REC, ""); err != nil {
                        klog.V(4).Infof("mount %s as writable error, detail: %s", path, err.Error())
                }
                if err := syscall.Mount(path, path, "none", syscall.MS_SLAVE|syscall.MS_BIND| syscall.MS_REC | syscall.MS_REMOUNT|syscall.MS_NOEXEC | getExtraMountFlags(path) ^ syscall.MS_RDONLY, ""); err != nil {
                        klog.V(4).Infof("remount %s as writable error, detail: %s", path, err.Error())
                }
	}
}

func applyReadOnlyPath(config *runtime.Config) {
	for _, path := range config.SecurityConfig.Security.File.ReadonlyPaths {
		if err := syscall.Mount(path, path, "none", syscall.MS_BIND | syscall.MS_REC, ""); err != nil {
			klog.V(4).Infof("bind mount %s error, detail: %s", path, err.Error())
		}
		if err := syscall.Mount(path, path, "none", syscall.MS_BIND | syscall.MS_REMOUNT | syscall.MS_RDONLY | syscall.MS_REC, ""); err != nil {
			klog.V(4).Infof("remount %s as readonly error, detail: %s", path, err.Error())
		}
	}
}

func applyUserAndGroup() {
	uid := os.Getuid()
	err := syscall.Setuid(uid)
	if err != nil {
		klog.Fatalf("change user error, detail: %s", err.Error())
	}
	gid := os.Getgid()
	err = syscall.Setgid(gid)
	if err != nil {
		klog.Fatalf("change user error, detail: %s", err.Error())
	}
}

// TODO: 目前antjail未引入process.userid, 当引入时需要参考runc finalizeNamespace
// applyRunlcCap ...
func applyRunlcCap(config *runtime.Config) {
	klog.V(4).Info("applyRunlcCap")
	if *config.SecurityConfig.System.NoNewPrivs {
		if err := unix.Prctl(unix.PR_SET_NO_NEW_PRIVS, 1, 0, 0, 0); err != nil {
			klog.Fatal("set PR_SET_NO_NEW_PRIVS error: %s", err.Error())
		}
	}

	cap, err := capability.NewPid2(0)
	//cap, err := capability.NewPid(int(C.gettid()))
	if err != nil {
		klog.Fatalf("get current cap error, detail: %s", err.Error())
	}


	allCapabilityTypes := capability.CAPS | capability.BOUNDING | capability.AMBIENT

	cap.Clear(allCapabilityTypes)

	unknownCaps := make(map[string]struct{})

	caps := map[capability.CapType][]capability.Cap{
		capability.BOUNDING:    capSlice(config.OCIConfig.Process.Capabilities.Bounding, unknownCaps),
		capability.EFFECTIVE:   capSlice(config.OCIConfig.Process.Capabilities.Effective, unknownCaps),
		capability.INHERITABLE: capSlice(config.OCIConfig.Process.Capabilities.Inheritable, unknownCaps),
		capability.PERMITTED:   capSlice(config.OCIConfig.Process.Capabilities.Permitted, unknownCaps),
		capability.AMBIENT:     capSlice(config.OCIConfig.Process.Capabilities.Ambient, unknownCaps),
	}

	if len(unknownCaps) > 0 {
		logrus.Warn("ignoring unknown or unavailable capabilities: ", mapKeys(unknownCaps))
	}

	capTypes := []capability.CapType{
		capability.BOUNDING,
		capability.PERMITTED,
		capability.INHERITABLE,
		capability.EFFECTIVE,
		capability.AMBIENT,
	}

	for _, capType := range capTypes {
		cap.Set(capType, caps[capType]...)
	}
	if err := cap.Apply(allCapabilityTypes); err != nil {
		klog.Fatalf("apply all cap error, detail :%s", err.Error())
	}
}

func applySeccomp(config *runtime.Config) {
	// Set seccomp as close to execve as possible, so as few syscalls take
	// place afterward (reducing the amount of syscalls that users need to
	// enable in their seccomp profiles).
	if config.OCIConfig.Linux.Seccomp != nil {
		sec, err := setupSeccomp(config.OCIConfig.Linux.Seccomp)
		if err != nil {
			klog.Fatal("specconv seccom error: %s", err.Error())
		}
		_, err = seccomp.InitSeccomp(sec)
		if err != nil {
			klog.Fatalf("install seccom error: %s", err.Error())
		}
	}
}


func applyMaskPath(config *runtime.Config) {
	for _, path := range config.OCIConfig.Linux.MaskedPaths {
		klog.V(4).Info("apply maskPath: ", path)/*
		if err := maskPath(path, config.OCIConfig.Linux.MountLabel); err != nil {
			klog.Fatal(err)
		}*/
                fileInfo, err := os.Lstat(path)
                if err != nil {
                        klog.Fatalf("error retrieving file information: %s", err.Error())
                }
                mode := fileInfo.Mode()
                if mode.IsDir() {
			if err := syscall.Mount("tmpfs", path, "tmpfs", syscall.MS_RDONLY, ""); err != nil  {
				klog.Fatal("can't make readonly: ", path)
			}
		}
		if mode.IsRegular() {
			if err := syscall.Mount("/dev/null", path, "none", syscall.MS_BIND | syscall.MS_REC | syscall.MS_PRIVATE | syscall.MS_RDONLY, ""); err != nil {
				klog.Fatalf("can't make file %s masked: %s", path, err.Error())
			}
		}
		klog.V(4).Info("apply maskPath ok: ", path)
	}
}

func getCurrentTaskPidNamespacerFD() (int, error) {
	filePath := fmt.Sprintf("/proc/%d/task/%d/ns/net", os.Getpid(), unix.Gettid())
	fd, err := unix.Open(filePath, unix.O_RDONLY|unix.O_CLOEXEC, 0)
	if err != nil {
		return -1, err
	}
	return fd, nil
}

func capSlice(caps []string, unknownCaps map[string]struct{}) []capability.Cap {
	var out []capability.Cap
	for _, c := range caps {
		if v, ok := capabilityMap[c]; !ok {
			unknownCaps[c] = struct{}{}
		} else {
			out = append(out, v)
		}
	}
	return out
}

// mapKeys returns the keys of input in sorted order
func mapKeys(input map[string]struct{}) []string {
	var keys []string
	for c := range input {
		keys = append(keys, c)
	}
	sort.Strings(keys)
	return keys
}

func setupSeccomp(config *specs.LinuxSeccomp) (*configs.Seccomp, error) {
	if config == nil {
		return nil, nil
	}

	// No default action specified, no syscalls listed, assume seccomp disabled
	if config.DefaultAction == "" && len(config.Syscalls) == 0 {
		return nil, nil
	}

	// We don't currently support seccomp flags.
	if len(config.Flags) != 0 {
		return nil, errors.New("seccomp flags are not yet supported by runc")
	}

	newConfig := new(configs.Seccomp)
	newConfig.Syscalls = []*configs.Syscall{}

	if len(config.Architectures) > 0 {
		newConfig.Architectures = []string{}
		for _, arch := range config.Architectures {
			newArch, err := seccomp.ConvertStringToArch(string(arch))
			if err != nil {
				return nil, err
			}
			newConfig.Architectures = append(newConfig.Architectures, newArch)
		}
	}

	// Convert default action from string representation
	newDefaultAction, err := seccomp.ConvertStringToAction(string(config.DefaultAction))
	if err != nil {
		return nil, err
	}
	newConfig.DefaultAction = newDefaultAction
	newConfig.DefaultErrnoRet = config.DefaultErrnoRet

	newConfig.ListenerPath = config.ListenerPath
	newConfig.ListenerMetadata = config.ListenerMetadata

	// Loop through all syscall blocks and convert them to libcontainer format
	for _, call := range config.Syscalls {
		newAction, err := seccomp.ConvertStringToAction(string(call.Action))
		if err != nil {
			return nil, err
		}

		for _, name := range call.Names {
			newCall := configs.Syscall{
				Name:     name,
				Action:   newAction,
				ErrnoRet: call.ErrnoRet,
				Args:     []*configs.Arg{},
			}
			// Loop through all the arguments of the syscall and convert them
			for _, arg := range call.Args {
				newOp, err := seccomp.ConvertStringToOperator(string(arg.Op))
				if err != nil {
					return nil, err
				}

				newArg := configs.Arg{
					Index:    arg.Index,
					Value:    arg.Value,
					ValueTwo: arg.ValueTwo,
					Op:       newOp,
				}

				newCall.Args = append(newCall.Args, &newArg)
			}
			newConfig.Syscalls = append(newConfig.Syscalls, &newCall)
		}
	}

	return newConfig, nil
}
