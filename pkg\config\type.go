package config

type SecurityConfig struct {
	Version string `json:"version"`
	Meta    struct {
		Name string `json:"name"`
		Arg  string `json:"arg"`
		Log  string `json:"log"`
	} `json:"meta"`
	System struct {
		Runtime  string `json:"runtime"`
		Rollback string `json:"rollback"`
		RollbackOverwrite map[string]interface{} `json:"rollbackOverWrite"`
		Accel    string `json:"accel"`
		NoNewPrivs	*bool `json:"noNewPrivs,omitempty"`
		Root     struct {
			Path     string `json:"path"`
			Paths	 []string `json:"paths"`
			Readonly *bool  `json:"readonly,omitempty"`
		} `json:"root"`
		Cwd string `json:"cwd"`
		Memory struct {
			Max int64 `json:"max"`
		} `json:"memory"`
		CPU struct {
			Number int64 `json:"number"`
		} `json:"cpu"`
	} `json:"system"`
	Security struct {
		File struct {
			MaskedPaths   []string `json:"maskedPaths"`
			ReadonlyPaths []string `json:"readonlyPaths"`
			WritablePaths []string `json:"writablePaths"`
			WritableNoexecPaths []string `json:"writableNoexecPaths"`
			PrivatePaths  []string `json:"privatePaths"`
		} `json:"file"`
		Network struct {
			Mode   string `json:"mode"`
			Policy struct {
				Listen []int    `json:"listen"`
				TCP    []string `json:"tcp"`
				UDP    []string `json:"udp"`
				DNS    []string `json:"dns"`
			} `json:"policy"`
		} `json:"network"`
		Cap struct {
			Add  []string `json:"add"`
			Drop []string `json:"drop"`
		} `json:"cap,omitempty"`
		Seccomp struct {
			Mode        string                `json:"mode"`
			SyscallList []LinuxSeccompSyscall `json:"syscallList,omitempty"`
		} `json:"seccomp"`
		Execve struct {
			Mode  string   `json:"mode"`
			Paths []string `json:"paths"`
		} `json:"execve"`
	} `json:"security"`
	Entrypoint []string `json:"cmd,omitempty"`
}
