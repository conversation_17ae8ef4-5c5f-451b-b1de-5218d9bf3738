# -*- mode: ruby -*-
# vi: set ft=ruby :

Vagrant.configure("2") do |config|
# Fedora box is used for testing cgroup v2 support
  config.vm.box = "fedora/38-cloud-base"
  config.vm.provider :virtualbox do |v|
    v.memory = 2048
    v.cpus = 2
  end
  config.vm.provider :libvirt do |v|
    v.memory = 2048
    v.cpus = 2
  end
  config.vm.provision "shell", inline: <<-SHELL
    set -e -u -o pipefail
    # Work around dnf mirror failures by retrying a few times
    for i in $(seq 0 2); do
      sleep $i
      # "config exclude" dnf shell command is not working in Fedora 35
      # (see https://bugzilla.redhat.com/show_bug.cgi?id=2022571);
      # the workaround is to specify it as an option.
      cat << EOF | dnf -y --exclude=kernel,kernel-core shell && break
config install_weak_deps false
update
install iptables gcc golang-go make glibc-static libseccomp-devel bats jq git-core criu fuse-sshfs
ts run
EOF
    done
    dnf clean all

    # Prevent the "fatal: unsafe repository" git complain during build.
    git config --global --add safe.directory /vagrant

    # Add a user for rootless tests
    useradd -u2000 -m -d/home/<USER>/bin/bash rootless

    # Allow root and rootless itself to execute `ssh rootless@localhost` in tests/rootless.sh
    ssh-keygen -t ecdsa -N "" -f /root/rootless.key
    mkdir -m 0700 -p /home/<USER>/.ssh
    cp /root/rootless.key /home/<USER>/.ssh/id_ecdsa
    cat /root/rootless.key.pub >> /home/<USER>/.ssh/authorized_keys
    chown -R rootless.rootless /home/<USER>

    # Delegate cgroup v2 controllers to rootless user via --systemd-cgroup
    mkdir -p /etc/systemd/system/user@.service.d
    cat > /etc/systemd/system/user@.service.d/delegate.conf << EOF
[Service]
# default: Delegate=pids memory
# NOTE: delegation of cpuset requires systemd >= 244 (Fedora >= 32, Ubuntu >= 20.04).
Delegate=yes
EOF
    systemctl daemon-reload
  SHELL
end
