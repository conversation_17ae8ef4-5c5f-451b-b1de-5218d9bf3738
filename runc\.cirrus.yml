---
# We use Cirrus for CentOS (native) and Fedora (in Vagrant), because neither
# CentOS nor Fedora is available on GHA natively, so the only option is VM.
# In GHA, nested virtualization is only supported on macOS instances, which
# are slow and flaky.

# NOTE Cirrus execution environments lack a terminal, needed for
# some integration tests. So we use `ssh -tt` command to fake a terminal.

task:
  timeout_in: 30m

  env:
    DEBIAN_FRONTEND: noninteractive
    HOME: /root
    # yamllint disable rule:key-duplicates
    matrix:
      DISTRO: fedora

  name: vagrant DISTRO:$DISTRO

  compute_engine_instance:
    image_project: cirrus-images
    image: family/docker-kvm
    platform: linux
    nested_virtualization: true
    # CPU limit: `16 / NTASK`: see https://cirrus-ci.org/faq/#are-there-any-limits
    cpu: 4
    # Memory limit: `4GB * NCPU`
    memory: 16G

  host_info_script: |
    uname -a
    # -----
    cat /etc/os-release
    # -----
    df -T
    # -----
    cat /proc/cpuinfo
  install_libvirt_vagrant_script: |
    curl -fsSL https://apt.releases.hashicorp.com/gpg | sudo gpg --dearmor -o /usr/share/keyrings/hashicorp-archive-keyring.gpg
    echo "deb [signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/hashicorp.list
    sudo sed -i 's/^# deb-src/deb-src/' /etc/apt/sources.list
    apt-get update
    apt-get install -y libvirt-daemon libvirt-daemon-system vagrant
    systemctl enable --now libvirtd
    apt-get build-dep -y vagrant ruby-libvirt
    apt-get install -y --no-install-recommends libxslt-dev libxml2-dev libvirt-dev ruby-bundler ruby-dev zlib1g-dev
    vagrant plugin install vagrant-libvirt
  vagrant_cache:
    fingerprint_script: cat Vagrantfile.$DISTRO
    folder: /root/.vagrant.d/boxes
  vagrant_up_script: |
    ln -sf Vagrantfile.$DISTRO Vagrantfile
    # Retry if it fails (download.fedoraproject.org returns 404 sometimes)
    vagrant up --no-tty || vagrant up --no-tty
    mkdir -p -m 0700 /root/.ssh
    vagrant ssh-config >> /root/.ssh/config
  guest_info_script: |
    ssh default 'sh -exc "uname -a && systemctl --version && df -T && cat /etc/os-release && go version"'
  check_config_script: |
    ssh default /vagrant/script/check-config.sh
  unit_tests_script: |
    ssh default 'sudo -i make -C /vagrant localunittest'
  integration_systemd_script: |
    ssh -tt default "sudo -i make -C /vagrant localintegration RUNC_USE_SYSTEMD=yes"
  integration_fs_script: |
    ssh -tt default "sudo -i make -C /vagrant localintegration"
  integration_systemd_rootless_script: |
    ssh -tt default "sudo -i make -C /vagrant localrootlessintegration RUNC_USE_SYSTEMD=yes"
  integration_fs_rootless_script: |
    ssh -tt default "sudo -i make -C /vagrant localrootlessintegration"

task:
  timeout_in: 30m

  env:
    HOME: /root
    CIRRUS_WORKING_DIR: /home/<USER>
    GO_VERSION: "1.19"
    BATS_VERSION: "v1.9.0"
    RPMS: gcc git iptables jq glibc-static libseccomp-devel make criu fuse-sshfs
    # yamllint disable rule:key-duplicates
    matrix:
      DISTRO: centos-7
      DISTRO: centos-stream-8
      DISTRO: centos-stream-9

  name: ci / $DISTRO

  compute_engine_instance:
    image_project: centos-cloud
    image: family/$DISTRO
    platform: linux
    cpu: 4
    memory: 8G

  install_dependencies_script: |
    case $DISTRO in
    centos-7)
      (cd /etc/yum.repos.d && curl -O https://copr.fedorainfracloud.org/coprs/adrian/criu-el7/repo/epel-7/adrian-criu-el7-epel-7.repo)
      # EPEL is needed for jq and fuse-sshfs.
      rpm -q epel-release || rpm -Uvh https://dl.fedoraproject.org/pub/epel/epel-release-latest-7.noarch.rpm
      # sysctl
      echo "user.max_user_namespaces=15076" > /etc/sysctl.d/userns.conf
      sysctl --system
      ;;
    centos-stream-8)
      yum config-manager --set-enabled powertools # for glibc-static
      ;;
    centos-stream-9)
      dnf config-manager --set-enabled crb # for glibc-static
      dnf -y install epel-release epel-next-release # for fuse-sshfs
      # Delegate all cgroup v2 controllers to rootless user via --systemd-cgroup.
      # The default (since systemd v252) is "pids memory cpu".
      mkdir -p /etc/systemd/system/user@.service.d
      printf "[Service]\nDelegate=yes\n" > /etc/systemd/system/user@.service.d/delegate.conf
      systemctl daemon-reload
      ;;
    esac
    # Work around dnf mirror failures by retrying a few times.
    for i in $(seq 0 2); do
      sleep $i
      yum install -y $RPMS && break
    done
    [ $? -eq 0 ] # fail if yum failed

    # Double check that all rpms were installed (yum from CentOS 7
    # does not exit with an error if some packages were not found).
    # Use --whatprovides since some packages are renamed.
    rpm -q --whatprovides $RPMS
    # install Go
    PREFIX="https://go.dev/dl/"
    # Find out the latest minor release URL.
    eval $(curl -fsSL "${PREFIX}?mode=json" | jq -r  --arg Ver "$GO_VERSION" '.[] | select(.version | startswith("go\($Ver)")) | .files[] | select(.os == "linux" and .arch == "amd64" and .kind == "archive") | "filename=\"" + .filename + "\""')
    curl -fsSL "$PREFIX$filename" | tar Cxz /usr/local
    # install bats
    cd /tmp
    git clone https://github.com/bats-core/bats-core
    cd bats-core
    git checkout $BATS_VERSION
    ./install.sh /usr/local
    cd -
    # Add a user for rootless tests
    useradd -u2000 -m -d/home/<USER>/bin/bash rootless
    # Allow root and rootless itself to execute `ssh rootless@localhost` in tests/rootless.sh
    ssh-keygen -t ecdsa -N "" -f /root/rootless.key
    mkdir -m 0700 -p /home/<USER>/.ssh
    cp /root/rootless.key /home/<USER>/.ssh/id_ecdsa
    cat /root/rootless.key.pub >> /home/<USER>/.ssh/authorized_keys
    chown -R rootless.rootless /home/<USER>
    # set PATH
    echo 'export PATH=/usr/local/go/bin:/usr/local/bin:$PATH' >> /root/.bashrc
    # Setup ssh localhost for terminal emulation (script -e did not work)
    ssh-keygen -t ed25519 -f /root/.ssh/id_ed25519 -N ""
    cat /root/.ssh/id_ed25519.pub >> /root/.ssh/authorized_keys
    chmod 400 /root/.ssh/authorized_keys
    ssh-keyscan localhost >> /root/.ssh/known_hosts
    echo -e "Host localhost\n\tStrictHostKeyChecking no\t\nIdentityFile /root/.ssh/id_ed25519\n" >> /root/.ssh/config
    sed -e "s,PermitRootLogin.*,PermitRootLogin prohibit-password,g" -i /etc/ssh/sshd_config
    systemctl restart sshd
  host_info_script: |
    uname -a
    # -----
    /usr/local/go/bin/go version
    # -----
    systemctl --version
    # -----
    cat /etc/os-release
    # -----
    df -T
    # -----
    cat /proc/cpuinfo
  check_config_script: |
    /home/<USER>/script/check-config.sh
  unit_tests_script: |
    ssh -tt localhost "make -C /home/<USER>"
  integration_systemd_script: |
    ssh -tt localhost "make -C /home/<USER>"
  integration_fs_script: |
    ssh -tt localhost "make -C /home/<USER>"
  integration_systemd_rootless_script: |
    case $DISTRO in
    centos-7|centos-stream-8)
      echo "SKIP: integration_systemd_rootless_script requires cgroup v2"
      ;;
    *)
      ssh -tt localhost "make -C /home/<USER>"
    esac
  integration_fs_rootless_script: |
    case $DISTRO in
    centos-7)
      echo "SKIP: FIXME: integration_fs_rootless_script is skipped because of EPERM on writing cgroup.procs"
        ;;
    *)
      ssh -tt localhost "make -C /home/<USER>"
      ;;
    esac
