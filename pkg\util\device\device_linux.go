//go:build linux
// +build linux

package device

import (
	"bufio"
	"os"
	"path/filepath"
	"strconv"
	"syscall"
)

// Major returns the major component of a Linux device number.
func Major(dev uint64) uint32 {
	major := uint32((dev & 0x00000000000fff00) >> 8)
	major |= uint32((dev & 0xfffff00000000000) >> 32)
	return major
}

// Minor returns the minor component of a Linux device number.
func Minor(dev uint64) uint32 {
	minor := uint32((dev & 0x00000000000000ff) >> 0)
	minor |= uint32((dev & 0x00000ffffff00000) >> 12)
	return minor
}

type DeviceNumber struct {
	Major string
	Minor string
}

func GetDeviceNumber(device string) (DeviceNumber, error) {
	dn := DeviceNumber{}
	stat := syscall.Stat_t{}
	err := syscall.Stat(device, &stat)
	if err != nil {
		return dn, err
	}

	dn.Major = strconv.FormatUint(uint64(Major(stat.Rdev)), 10)
	dn.Minor = strconv.FormatUint(uint64(Minor(stat.Rdev)), 10)
	return dn, nil
}

// TODO: SetSandBoxDeviceAccel 应该由cgroup发起
func SetSandBoxDeviceAccel(sandbox, device string) error {
	stat := syscall.Stat_t{}
	err := syscall.Stat(device, &stat)
	if err != nil {
		return err
	}

	dn, err := GetDeviceNumber(device)
	if err != nil {
		return err
	}

	devCgrData := "c " + dn.Major + ":" + dn.Minor + " rwm"

	fdp := filepath.Dir(sandbox)
	dfc, err := os.Open(filepath.Join(fdp, "devices.list"))
	if err != nil {
		return err
	}
	sl := bufio.NewScanner(dfc)
	sl.Split(bufio.ScanLines)
	deviceAllowExist := false
	for sl.Scan() {
		if sl.Text() == devCgrData {
			deviceAllowExist = true
		}
	}
	if !deviceAllowExist {
		return os.WriteFile(sandbox, []byte(devCgrData), 0666)
	}
	return nil

}
