package runsc

import (
	"context"
	"os"
	"os/exec"
	"path/filepath"
	"sync"
	"syscall"
	goruntime "runtime"

	accelPkg "code.alipay.com/antjail/antjail/pkg/accel"
	"code.alipay.com/antjail/antjail/pkg/cgroup"
	"code.alipay.com/antjail/antjail/pkg/config"
	"code.alipay.com/antjail/antjail/pkg/librunlc"
	"code.alipay.com/antjail/antjail/pkg/runtime"
	"code.alipay.com/antjail/antjail/pkg/util"
	"k8s.io/klog/v2"
)

var runscBinaryDir = "/etc/antjail/bin"
var runscRootlessDir = "/var/tmp"

var nanovisorRunscfile = "nanovisor_runsc"
var gvisorRunscfile = "gvisor_runsc"

type runsc struct {
	delegateClearFun []func()
	once             *sync.Once
	runscfile        string
}

const runscRuntime runtime.Runtime = "runsc"

func init() {
	runtime.Register(&runsc{once: &sync.Once{}})
}

func (r *runsc) Type() runtime.Runtime {
	return runscRuntime
}

func (r *runsc) Run(ctx context.Context, flags []string, config runtime.Config) {
	klog.V(4).Info("prepare runsc config")

	if runtime.Rootless {
		klog.V(4).Info("set euid as uid and egid as gid")
		if err := syscall.Seteuid(os.Getuid()); err != nil {
			klog.Fatalf("change euid as uid %s error %s", os.Getuid(), err.Error())
		}
		if err := syscall.Setegid(os.Getgid()); err != nil {
			klog.Fatalf("change egid as gid %s error %s", os.Getgid(), err.Error())
		}
		runscBinaryDir = filepath.Join(runscRootlessDir, ".antjail")
	}
	runscFlags := []string{}

	if err := r.depCheck(); err != nil {
		if config.SecurityConfig.System.Rollback == "cmd" {
			klog.V(4).Info("will change to cmd")
			r.runWithoutSandBox(ctx, flags, config)
			return
		} else if config.SecurityConfig.System.Rollback == "runlc" {
			klog.V(4).Info("will change to runlc")
			lr := librunlc.Runlc{}
			applyRollbackOverWrite(config.SecurityConfig)
			lr.Run(ctx, flags, &config, false)
			return
		}
		klog.Fatal(err)
		return
	}

	// 需先清理sandbox，否则部分cgroup会清理失败
	r.delegateClearFun = append(r.delegateClearFun, func() { r.clearRunscSandbox(config) })

	// load accel
	if accel := config.SecurityConfig.System.Accel; accel != "" {
		klog.V(4).Info("accel: ", accel)
		ac, err := accelPkg.GetAccel(accel)
		if err == accelPkg.NotFoudErr {
			klog.Fatalf("not found accel %s", accel)
		}
		if err := ac.Load(config.OCIConfig); err != nil {
			klog.Fatalf("load accel %s error, detail: %s", accel, err.Error())
		}
		r.delegateClearFun = append(r.delegateClearFun, ac.Unload)
		runscFlags = append(runscFlags, "--platform", accel)
	}

	goruntime.LockOSThread()
	defer goruntime.UnlockOSThread()

	runscFlags = append(runscFlags, "--cpu-num-from-quota")
	runscFlags = append(runscFlags, r.prepareRunsc(flags, &config)...)
	// write config to json
	cmdlineFlags := []string{}
	if len(runscFlags) > 0 {
		cmdlineFlags = append(cmdlineFlags, runscFlags...)
	}
	cmdlineFlags = append(cmdlineFlags, "run", config.SecurityConfig.Meta.Name)
	cmd := util.CommandContextWithSignal(ctx, r.runscfile, r.sendSignalToSandbox(config), cmdlineFlags...)
	cmd.Dir = filepath.Join(runtime.Srootdir, config.SecurityConfig.Meta.Name)
	klog.V(4).Infof("will run %s", cmd.String())

	r.delegateClearFun = append(r.delegateClearFun, func() { r.clear(config) })

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	cmd.Stdin = os.Stdin

	defer r.runDelegateClearInExit(0)

	klog.V(4).Info("start run user command")
	klog.V(4).Infof("euid %d", os.Geteuid())
	if err := cmd.Run(false); err != nil {
		if exiterr, ok := err.(*exec.ExitError); ok {
			os.Exit(r.runDelegateClearInExit(exiterr.ExitCode()))
		} else {
			klog.Fatalf("cmd.Wait: %v", err)
		}
	}
	klog.V(4).Info("end run user command")
}

func (r *runsc) runWithoutSandBox(ctx context.Context, args []string, config runtime.Config) {
	if len(args) == 0 {
		klog.Fatal("not found args")
	}
	var cmd *exec.Cmd
	if len(args) == 1 {
		cmd = exec.CommandContext(ctx, args[0])
	} else {
		cmd = exec.CommandContext(ctx, args[0], args[1:]...)
	}
	klog.V(4).Infof("will run %s without sandbox", cmd.String())
	applyUserAndGroup()

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	cmd.Stdin = os.Stdin

	klog.V(4).Info("start run user command without sandbox")
	if err := cmd.Run(); err != nil {
		if exiterr, ok := err.(*exec.ExitError); ok {
			os.Exit(r.runDelegateClearInExit(exiterr.ExitCode()))
		} else {
			klog.Fatalf("cmd.Wait: %v", err)
		}
	}
	klog.V(4).Info("end run user command without sandbox")
}

func (r *runsc) runDelegateClearInExit(code int) int {
	r.once.Do(func() {
		for _, f := range r.delegateClearFun {
			f()
		}
	})
	return code
}

func (r runsc) clearRunscSandbox(config runtime.Config) {
	cmd := exec.Command(r.runscfile, "delete", "--force", config.SecurityConfig.Meta.Name)
	if err := cmd.Run(); err != nil {
		klog.V(4).Info(err.Error())
	}
}

// SupportMode ...
func (r *runsc) SupportMode() []string {
	return []string{"rootless"}
}

func (r runsc) clear(config runtime.Config) {
	// os.RemoveAll("/tmp/antjail_dev")
	// os.Remove("/dev/kvm")
	//os.RemoveAll(filepath.Join(runtime.Srootdir, config.SecurityConfig.Meta.Name))
	// TODO: cgroup
	os.RemoveAll(filepath.Join(cgroup.CgroupV1Root, "cpu", config.OCIConfig.Linux.CgroupsPath))
	os.RemoveAll(filepath.Join(cgroup.CgroupV1Root, "memory", config.OCIConfig.Linux.CgroupsPath))

}

func applyUserAndGroup() {
	uid := os.Getuid()
	err := syscall.Setuid(uid)
	if err != nil {
		klog.Fatalf("change user error, detail: %s", err.Error())
	}
	gid := os.Getgid()
	err = syscall.Setgid(gid)
	if err != nil {
		klog.Fatalf("change user error, detail: %s", err.Error())
	}
}



func applyRollbackOverWrite(config *config.SecurityConfig) {
	if config.System.RollbackOverwrite == nil {
		return
	}
	mode, ok := config.System.RollbackOverwrite["security.network.mode"]
	if !ok {
		return
	}
	modeString, ok := mode.(string)
	if !ok {
		return
	}
	config.Security.Network.Mode = modeString
}
