# Swedish translation for polkit.
# Copyright © 2015 polkit's COPYRIGHT HOLDER
# This file is distributed under the same license as the polkit package.
# <PERSON> <<EMAIL>>, 2015.
#
msgid ""
msgstr ""
"Project-Id-Version: polkit master\n"
"Report-Msgid-Bugs-To: https://bugs.freedesktop.org/enter_bug.cgi?"
"product=PolicyKit&keywords=I18N+L10N&component=libpolkit\n"
"POT-Creation-Date: 2015-10-21 12:21+0000\n"
"PO-Revision-Date: 2015-11-19 23:48+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Swedish <<EMAIL>>\n"
"Language: sv\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 1.8.6\n"

#: ../actions/org.freedesktop.policykit.policy.in.h:1
msgid "Run a program as another user"
msgstr "Kör ett program som en annan användare"

#: ../actions/org.freedesktop.policykit.policy.in.h:2
msgid "Authentication is required to run a program as another user"
msgstr "Autentisering krävs för att köra ett program som en annan användare"

#: ../src/examples/org.freedesktop.policykit.examples.pkexec.policy.in.h:1
msgid "Run the polkit example program Frobnicate"
msgstr "Kör polkit-exempelprogrammet Frobnicate"

#: ../src/examples/org.freedesktop.policykit.examples.pkexec.policy.in.h:2
msgid ""
"Authentication is required to run the polkit example program Frobnicate "
"(user=$(user), user.gecos=$(user.gecos), user.display=$(user.display), "
"program=$(program), command_line=$(command_line))"
msgstr ""
"Autentisering krävs för att köra polkit-exempelprogrammet Frobnicate (user="
"$(user), user.gecos=$(user.gecos), user.display=$(user.display), program="
"$(program), command_line=$(command_line))"

#: ../src/programs/pkaction.c:100
msgid "Only output information about ACTION"
msgstr "Visa endast information om ÅTGÄRD"

#: ../src/programs/pkaction.c:100
msgid "ACTION"
msgstr "ÅTGÄRD"

#: ../src/programs/pkaction.c:104
msgid "Output detailed action information"
msgstr "Visa detaljerad åtgärdsinformation"

#: ../src/programs/pkaction.c:108 ../src/programs/pkttyagent.c:61
msgid "Show version"
msgstr "Visa version"

#: ../src/programs/pkaction.c:130
msgid "[--action-id ACTION]"
msgstr "[--action-id ÅTGÄRD]"

#: ../src/programs/pkaction.c:131 ../src/programs/pkttyagent.c:81
#, c-format
msgid ""
"Report bugs to: %s\n"
"%s home page: <%s>"
msgstr ""
"Rapportera fel till: %s\n"
"Webbplats för %s: <%s>"

#: ../src/programs/pkaction.c:145 ../src/programs/pkcheck.c:491
#: ../src/programs/pkttyagent.c:95
#, c-format
msgid "%s: Unexpected argument `%s'\n"
msgstr "%s: Oväntat argument ”%s”\n"

#: ../src/programs/pkcheck.c:35
#, c-format
msgid ""
"Usage:\n"
"  pkcheck [OPTION...]\n"
"\n"
"Help Options:\n"
"  -h, --help                         Show help options\n"
"\n"
"Application Options:\n"
"  -a, --action-id=ACTION             Check authorization to perform ACTION\n"
"  -u, --allow-user-interaction       Interact with the user if necessary\n"
"  -d, --details=KEY VALUE            Add (KEY, VALUE) to information about "
"the action\n"
"  --enable-internal-agent            Use an internal authentication agent if "
"necessary\n"
"  --list-temp                        List temporary authorizations for "
"current session\n"
"  -p, --process=PID[,START_TIME,UID] Check authorization of specified "
"process\n"
"  --revoke-temp                      Revoke all temporary authorizations for "
"current session\n"
"  -s, --system-bus-name=BUS_NAME     Check authorization of owner of "
"BUS_NAME\n"
"  --version                          Show version\n"
"\n"
"Report bugs to: %s\n"
"%s home page: <%s>\n"
msgstr ""
"Användning:\n"
"  pkcheck [FLAGGA…]\n"
"\n"
"Hjälpflaggor:\n"
"  -h, --help                         Visa hjälpflaggor\n"
"\n"
"Programflaggor:\n"
"  -a, --action-id=ÅTGÄRD             Kontrollera auktorisering för att "
"utföra ÅTGÄRD\n"
"  -u, --allow-user-interaction       Interagera med användaren om "
"nödvändigt\n"
"  -d, --details=NYCKEL VÄRDE           Lägg till (NYCKEL, VÄRDE) till "
"information om åtgärden\n"
"  --enable-internal-agent            Använd en intern autentiseringsagent om "
"nödvändigt\n"
"  --list-temp                        Lista tillfälliga auktoriseringar för "
"aktuell session\n"
"  -p, --process=PID[,STARTTID,UID] Kontrollera auktorisering för angiven "
"process\n"
"  --revoke-temp                      Återkalla alla tillfälliga "
"auktoriseringar för aktuell session\n"
"  -s, --system-bus-name=BUSSNAMN     Kontrollera auktorisering för ägare av "
"BUSSNAMN\n"
"  --version                          Visa version\n"
"\n"
"Rapportera fel till: %s\n"
"Webbplats för %s: <%s>\n"

#: ../src/programs/pkcheck.c:391 ../src/programs/pkcheck.c:424
#: ../src/programs/pkcheck.c:436
#, c-format
msgid "%s: Argument expected after `%s'\n"
msgstr "%s: Argument förväntades efter ”%s”\n"

#: ../src/programs/pkcheck.c:414
#, c-format
msgid "%s: Invalid --process value `%s'\n"
msgstr "%s: Ogiltigt värde ”%s” för --process\n"

#: ../src/programs/pkcheck.c:451 ../src/programs/pkcheck.c:460
#, c-format
msgid "%s: Two arguments expected after `--detail'\n"
msgstr "%s: Två argument förväntades efter ”--detail”\n"

#: ../src/programs/pkcheck.c:521
#, c-format
msgid "%s: Subject not specified\n"
msgstr "%s: Subjekt ej angivet\n"

#. Translators: message shown when trying to run a program as root. Do not
#. * translate the $(program) fragment - it will be expanded to the path
#. * of the program e.g.  /bin/bash.
#.
#: ../src/programs/pkexec.c:794
msgid "Authentication is needed to run `$(program)' as the super user"
msgstr "Autentisering krävs för att köra ”$(program)” som superanvändaren"

#. Translators: message shown when trying to run a program as another user.
#. * Do not translate the $(program) or $(user) fragments - the former will
#. * be expanded to the path of the program e.g. "/bin/bash" and the latter
#. * to the user e.g. "John Doe (johndoe)" or "johndoe".
#.
#: ../src/programs/pkexec.c:804
msgid "Authentication is needed to run `$(program)' as user $(user.display)"
msgstr ""
"Autentisering krävs för att köra ”$(program)” som användaren $(user.display)"

#: ../src/programs/pkttyagent.c:44
msgid "Don't replace existing agent if any"
msgstr "Ersätt inte existerande agent om sådan finns"

#: ../src/programs/pkttyagent.c:48
msgid "Close FD when the agent is registered"
msgstr "Stäng FD då agenten registrerats"

#: ../src/programs/pkttyagent.c:48
msgid "FD"
msgstr "FD"

#: ../src/programs/pkttyagent.c:52
msgid "Register the agent for the specified process"
msgstr "Registrera agenten för den angivna processen"

#: ../src/programs/pkttyagent.c:53
msgid "PID[,START_TIME]"
msgstr "PID[,STARTTID]"

# https://bugs.freedesktop.org/show_bug.cgi?id=92581
#: ../src/programs/pkttyagent.c:57
msgid "Register the agent owner of BUS_NAME"
msgstr "Registrera agenten för ägaren av BUSSNAMN"

#: ../src/programs/pkttyagent.c:57
msgid "BUS_NAME"
msgstr "BUSSNAMN"

#: ../src/programs/pkttyagent.c:127
#, c-format
msgid "%s: Invalid process specifier `%s'\n"
msgstr "%s: Ogiltig processbeskrivare ”%s”\n"
