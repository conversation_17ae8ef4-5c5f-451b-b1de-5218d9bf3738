package runtime

// config 和 flag 等后续根据实际情况看是否需要切分
import (
	"context"
	goruntime "runtime"
	"io/ioutil"
	"os"
	"os/signal"
	"path/filepath"
	"strings"
	"syscall"
	"log"
	"fmt"
	"strconv"
	"code.alipay.com/antjail/antjail/pkg/config"
	"code.alipay.com/antjail/antjail/pkg/util"
	oci "github.com/opencontainers/runtime-spec/specs-go"
	//"github.com/google/uuid"

	"k8s.io/klog/v2"
)

const DefaultSrootdir = "/var/run/antjail/"
const RootlessDefaultSrootDir = "/var/tmp/antjail"

var Srootdir = ""
//var ChangeSrootdirInPrivateProcess = false

var Rootless = false

var Daemon = false

var NoCgroup = false

type Config struct {
	SecurityConfig *config.SecurityConfig
	OCIConfig      *oci.Spec
}

type SandboxRuntime interface {
	Type() Runtime
	// run 自清理
	Run(ctx context.Context, flags []string, config Config)

	SupportMode() []string
}

type Runtime string

var sandboxRuntimes map[Runtime]SandboxRuntime = make(map[Runtime]SandboxRuntime)

func Register(r SandboxRuntime) {
	sandboxRuntimes[r.Type()] = r
}

func getExtraMountFlags(path string) uintptr {
        var ret uintptr
        content, err := ioutil.ReadFile("/proc/self/mountinfo")
        if err != nil {
                klog.Fatalf("read /proc/mounts error:%s", err.Error())
                return ret
        }

        lines := strings.Split(string(content), "\n")

        for _, line := range lines {
                line = strings.TrimSpace(line)
                if line == "" {
                        continue
                }
                fields := strings.Fields(line)
                if path == fields[4] {
                        if strings.Contains(fields[5], "nosuid") {
                                ret |= syscall.MS_NOSUID
                        }
                        if strings.Contains(fields[5], "noexec") {
                                ret |= syscall.MS_NOEXEC
                        }
                        if strings.Contains(fields[5], "nodev") {
                                ret |= syscall.MS_NODEV
                        }
                        if strings.Contains(fields[5], "ro") {
                                ret |= syscall.MS_RDONLY
                        }

                }
                //fmt.Printf("Device: %s, MountPoint: %s, FileSystemType: %s, Options: %s\n", fields[0], fields[1], fields[2], strings.Join(fields[3:], ", "))
        }
        return ret
}


func listDir(dir string) {
    // 指定要读取的目录

    // 打开目录
    f, err := os.Open(dir)
    if err != nil {
        log.Fatal(err)
    }
    defer f.Close()

    // 读取目录内容
    files, err := f.Readdir(-1) // -1 表示读取所有条目
    if err != nil {
        log.Fatal(err)
    }

    // 遍历目录内容
    for _, file := range files {
        if file.IsDir() {
            fmt.Println("目录:", file.Name())
        } else {
            fmt.Println("文件:", file.Name())
        }
    }
}


func ProcessPrivatePaths(securityConfig config.SecurityConfig) {
        privatepaths := securityConfig.Security.File.PrivatePaths
        if len(privatepaths) == 0 {
                return
        }

	klog.V(4).Info("process privatepaths")

	syscall.Umask(0000)

        if err := syscall.Unshare(syscall.CLONE_NEWNS); err != nil {
                klog.Fatalf("unshare new mount namespace error, detail: %s", err.Error())
                return
        }

	syscall.Mount("none", "/", "", syscall.MS_REC|syscall.MS_SLAVE, "")
	for _, v := range privatepaths {
		pDirPath := filepath.Dir(v)
		//if pDirPath == "/tmp" {
		//	ChangeSrootdirInPrivateProcess = true
	//	}
		newDir := filepath.Join(Srootdir, pDirPath)
		if _, err := os.Stat(newDir); os.IsNotExist(err) {
			err := os.MkdirAll(newDir, 0755)
			if err != nil {
				klog.Fatalf("can't create private dir:%s, error:%s", newDir, err.Error())
				return
			}
		}
		klog.V(4).Info("create dir successly:%s", newDir)
		if err := syscall.Mount(pDirPath, newDir, "", syscall.MS_BIND | syscall.MS_REC | syscall.MS_SLAVE,  ""); err != nil {
			klog.Fatalf("can't mount %s to %s, error:%s", pDirPath, newDir, err.Error())
			return
		}
		klog.V(4).Infof("mount %s to %s successfully", pDirPath, newDir)
		//listDir(newDir)
	}
	/*
	if _, err := os.Stat("/var/run/antjail/tmp"); os.IsNotExist(err) {
                err := os.MkdirAll("/var/run/antjail/tmp", 0755)
                if err != nil {
                        klog.Fatalf("can't create tmp: %v", err)
                        return
                }
                klog.V(4).Info("create tmp successfully")
		if err := syscall.Mount("/tmp", "/var/run/antjail/tmp", "", syscall.MS_BIND| syscall.MS_SLAVE, ""); err != nil {
			klog.Fatal("can't bind mount /tmp, %s", err.Error())
			return
		}

        } else {
                klog.V(4).Info("the tmp exist")
        }*/
/*
        if err := syscall.Unshare(syscall.CLONE_NEWNS); err != nil {
                klog.Fatalf("unshare new mount namespace error, detail: %s", err.Error())
                return
        }*/

        //syscall.Mount("none", "/", "", syscall.MS_REC|syscall.MS_SLAVE, "")

	for _, v := range privatepaths {
		pDirPath := filepath.Dir(v)
		if err := syscall.Mount(pDirPath, pDirPath, "none", syscall.MS_BIND | syscall.MS_REC | syscall.MS_SLAVE, ""); err != nil {
			klog.Fatalf("mount %s to mountpoint error:%s", pDirPath, err.Error())
			return
		}
		if err := syscall.Mount("tmpfs", pDirPath, "tmpfs", 0, ""); err != nil {
			klog.Fatalf("mount tmpfs to %s error:%s", pDirPath, err.Error())
			return
		}
		klog.V(4).Infof("mount tmpfs to %s successfully", pDirPath)
		//listDir(pDirPath)
	}
	for _, v := range privatepaths {
		pDirPath := filepath.Dir(v)
		srcPath := strings.Replace(v, pDirPath, filepath.Join(Srootdir, pDirPath), 1)
		pDirInfo, err := os.Stat(srcPath)
		if err != nil {
			klog.Fatalf("can't get the %s file info, error: %s", pDirPath, err.Error())
			return
		}

		if err := os.MkdirAll(v, pDirInfo.Mode().Perm()); err != nil {
			klog.Fatalf("can't create privatePaths dir:%s, %s", v, err.Error())
			return
		}

		stat, ok := pDirInfo.Sys().(*syscall.Stat_t)
		if !ok {
			klog.Fatalf("can't get the statinfo %s", pDirPath)
			return
		}

		if err := syscall.Chown(v, int(stat.Uid), int(stat.Gid)); err != nil {
			klog.Fatalf("can't change the uid and gid of %s, uid:%v, gid: %v: error:%s", v, stat.Uid, stat.Gid, err.Error())
			return
		}

		if err := syscall.Mount(srcPath, v, "", syscall.MS_BIND, ""); err != nil {
			klog.Fatalf("can't bind mount privatePaths: %s, %s, %s", srcPath, v, err.Error())
			return
		}
		klog.V(4).Infof("mount %s to %s successfully", srcPath, v)
		//listDir("/var/run/antjail/tmp/test")
		//listDir("/tmp/test")

	}
	/*
        if err := syscall.Mount("tmpfs", "/tmp", "tmpfs",
                uintptr(syscall.MS_NOEXEC | syscall. MS_NOSUID | syscall.MS_NODEV), ""); err != nil {
                klog.Fatalf("mount firstDir tmpfs error, detail:%s, %s", "/tmp", err.Error())
                return
        }*/
	/*
        for _, v := range privatepaths {
		srcPath := strings.Replace(v, "/tmp", "/var/run/antjail/tmp", 1)
		if err := os.MkdirAll(v, 0755); err != nil {
                        klog.Fatalf("can't create the privatePaths dir:%s,  %s",v, err.Error())
                        return
                }
                if err := syscall.Mount(srcPath, v, "", syscall.MS_BIND, ""); err != nil {
                        klog.Fatalf("can't bind mount privatepaths:%s, %s", v, err.Error())
			return
                }
        }*/


}


func Run(runtime Runtime, flags []string, securityConfig config.SecurityConfig) {
	if r, ok := sandboxRuntimes[runtime]; !ok {
		klog.Fatal("unknow runtime")
	} else {
		supportMode := r.SupportMode()
		if Rootless && !util.SliceContainItem(supportMode, "rootless") {
			klog.Fatalf("runtime %s not support %s mode", runtime, "rootless")
		}
		if Daemon && !util.SliceContainItem(supportMode, "daemon") {
			klog.Fatalf("runtime %s not support %s mode", runtime, "daemon")
		}

		if Srootdir == "" && Rootless {
			Srootdir = RootlessDefaultSrootDir + strconv.Itoa(os.Getuid())
		}
		if Srootdir == "" {
			Srootdir = DefaultSrootdir
		}

		goruntime.LockOSThread()
		defer goruntime.UnlockOSThread()
		privatepaths := securityConfig.Security.File.PrivatePaths
		if len(privatepaths) != 0 {
			ProcessPrivatePaths(securityConfig)
		/*	if ChangeSrootdirInPrivateProcess {
				Srootdir="/var/run/antjail/tmp"
			}*/
		}

	//	ProcessPrivatePaths(securityConfig)
		ctx := watchSignal()
		r.Run(ctx, flags, Config{
			SecurityConfig: &securityConfig,
			OCIConfig:      config.MergeSecurityConfigWithTemplate(&securityConfig),
		})
	}
}

func watchSignal() context.Context {
	ctx := context.Background()
	ctx, cancel := context.WithCancel(ctx)
	osSignal := make(chan os.Signal, 1)
	signal.Notify(osSignal, syscall.SIGTERM, syscall.SIGQUIT, syscall.SIGHUP, syscall.SIGINT)
	ctx = context.WithValue(ctx, "signal", osSignal)
	ctx = context.WithValue(ctx, "cancelFunc", cancel)
	return ctx
}
