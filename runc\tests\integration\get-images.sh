#!/bin/bash

# DO NOT EDIT!  Generated by "bootstrap-get-images.sh"

# This script checks if container images needed for tests (currently
# busybox and Debian 10 "Buster") are available locally, and downloads
# them to testdata directory if not.
#
# The script is self-contained/standalone and is used from a few places
# that need to ensure the images are downloaded. Its output is suitable
# for consumption by shell via eval (see helpers.bash).

set -e -u -o pipefail

# Root directory of integration tests.
INTEGRATION_ROOT=$(dirname "$(readlink -f "${BASH_SOURCE[0]}")")
# Test data path.
TESTDATA="${INTEGRATION_ROOT}/testdata"
# Sanity check: $TESTDATA directory must exist.
if [ ! -d "$TESTDATA" ]; then
	echo "Bad TESTDATA directory: $TESTDATA. Aborting" >&2
	exit 1
fi

function get() {
	local dest="$1" url="$2"

	[ -e "$dest" ] && return

	# Sanity check: $TESTDATA directory must be writable.
	if [ ! -w "$TESTDATA" ]; then
		echo "TESTDATA directory ($TESTDATA) not writable. Aborting" >&2
		exit 1
	fi

	if ! curl -o "$dest" -fsSL --retry 5 "$url"; then
		echo "Failed to get $url" 1>&2
		exit 1
	fi
}

arch=$(go env GOARCH)
if [ "$arch" = 'arm' ]; then
	arm=$(go env GOARM)
	: "${arm:=7}"
	arch=${arch}v$arm
fi

case $arch in
amd64)
	# https://github.com/docker-library/busybox/tree/dist-amd64
	# https://github.com/docker-library/busybox/tree/31d342ad033e27c18723a516a2274ab39547be27/stable/glibc
	url="https://github.com/docker-library/busybox/raw/31d342ad033e27c18723a516a2274ab39547be27/stable/glibc/busybox.tar.xz"
	;;

armv5)
	# https://github.com/docker-library/busybox/tree/dist-arm32v5
	# https://github.com/docker-library/busybox/tree/96ea82ea25565f78b50bd032d5768d64985d6e11/stable/glibc
	url="https://github.com/docker-library/busybox/raw/96ea82ea25565f78b50bd032d5768d64985d6e11/stable/glibc/busybox.tar.xz"
	;;

armv7)
	# https://github.com/docker-library/busybox/tree/dist-arm32v7
	# https://github.com/docker-library/busybox/tree/5cb6c347469e86e4468e5e248de751b3598bb577/stable/glibc
	url="https://github.com/docker-library/busybox/raw/5cb6c347469e86e4468e5e248de751b3598bb577/stable/glibc/busybox.tar.xz"
	;;

arm64)
	# https://github.com/docker-library/busybox/tree/dist-arm64v8
	# https://github.com/docker-library/busybox/tree/94c664b5ca464546266bce54be0082874a44c7b2/stable/glibc
	url="https://github.com/docker-library/busybox/raw/94c664b5ca464546266bce54be0082874a44c7b2/stable/glibc/busybox.tar.xz"
	;;

386)
	# https://github.com/docker-library/busybox/tree/dist-i386
	# https://github.com/docker-library/busybox/tree/461a473aef31b7726ea99909a24551bf44565c05/stable/glibc
	url="https://github.com/docker-library/busybox/raw/461a473aef31b7726ea99909a24551bf44565c05/stable/glibc/busybox.tar.xz"
	;;

mips64le)
	# https://github.com/docker-library/busybox/tree/dist-mips64le
	# https://github.com/docker-library/busybox/tree/47f73f7c735dcd6760a976bfe0012d251b6ef0a9/stable/glibc
	url="https://github.com/docker-library/busybox/raw/47f73f7c735dcd6760a976bfe0012d251b6ef0a9/stable/glibc/busybox.tar.xz"
	;;

ppc64le)
	# https://github.com/docker-library/busybox/tree/dist-ppc64le
	# https://github.com/docker-library/busybox/tree/9ca13bc214717966383cf97e08606b444b7300e4/stable/glibc
	url="https://github.com/docker-library/busybox/raw/9ca13bc214717966383cf97e08606b444b7300e4/stable/glibc/busybox.tar.xz"
	;;

s390x)
	# https://github.com/docker-library/busybox/tree/dist-s390x
	# https://github.com/docker-library/busybox/tree/a03814d21bcf97767121bb9422a742ec237a09e2/stable/glibc
	url="https://github.com/docker-library/busybox/raw/a03814d21bcf97767121bb9422a742ec237a09e2/stable/glibc/busybox.tar.xz"
	;;

*)
	echo >&2 "error: unsupported busybox architecture: $arch"
	exit 1
	;;
esac
rootfs="$TESTDATA/busybox-${arch}.tar.xz"
get "$rootfs" "$url"
var=busybox_image
echo "${var^^}=$rootfs"

case $arch in
amd64)
	# https://github.com/debuerreotype/docker-debian-artifacts/tree/dist-amd64
	# https://github.com/debuerreotype/docker-debian-artifacts/tree/686d9f6eaada08a754bc7abf6f6184c65c5b378f/buster/slim
	url="https://github.com/debuerreotype/docker-debian-artifacts/raw/686d9f6eaada08a754bc7abf6f6184c65c5b378f/buster/slim/rootfs.tar.xz"
	;;

armv5)
	# https://github.com/debuerreotype/docker-debian-artifacts/tree/dist-arm32v5
	# https://github.com/debuerreotype/docker-debian-artifacts/tree/155640b6e2e249dfaeee8795d5de539ef3e49417/buster/slim
	url="https://github.com/debuerreotype/docker-debian-artifacts/raw/155640b6e2e249dfaeee8795d5de539ef3e49417/buster/slim/rootfs.tar.xz"
	;;

armv7)
	# https://github.com/debuerreotype/docker-debian-artifacts/tree/dist-arm32v7
	# https://github.com/debuerreotype/docker-debian-artifacts/tree/60ff0c2c6ce9556e5d8a2758dd2b3f3731716a6f/buster/slim
	url="https://github.com/debuerreotype/docker-debian-artifacts/raw/60ff0c2c6ce9556e5d8a2758dd2b3f3731716a6f/buster/slim/rootfs.tar.xz"
	;;

arm64)
	# https://github.com/debuerreotype/docker-debian-artifacts/tree/dist-arm64v8
	# https://github.com/debuerreotype/docker-debian-artifacts/tree/2f108af35e22064c848b8628a7cac56192246dba/buster/slim
	url="https://github.com/debuerreotype/docker-debian-artifacts/raw/2f108af35e22064c848b8628a7cac56192246dba/buster/slim/rootfs.tar.xz"
	;;

386)
	# https://github.com/debuerreotype/docker-debian-artifacts/tree/dist-i386
	# https://github.com/debuerreotype/docker-debian-artifacts/tree/e4db8aa97f4366e6f27ddbdeaed0773fe0288d47/buster/slim
	url="https://github.com/debuerreotype/docker-debian-artifacts/raw/e4db8aa97f4366e6f27ddbdeaed0773fe0288d47/buster/slim/rootfs.tar.xz"
	;;

mips64le)
	# https://github.com/debuerreotype/docker-debian-artifacts/tree/dist-mips64le
	# https://github.com/debuerreotype/docker-debian-artifacts/tree/e28cbd76dcfba10446b1722aebb5a996121e3d27/buster/slim
	url="https://github.com/debuerreotype/docker-debian-artifacts/raw/e28cbd76dcfba10446b1722aebb5a996121e3d27/buster/slim/rootfs.tar.xz"
	;;

ppc64le)
	# https://github.com/debuerreotype/docker-debian-artifacts/tree/dist-ppc64le
	# https://github.com/debuerreotype/docker-debian-artifacts/tree/3ba08903ca3fd48fe59ba92b02744a2f5d4d9d6f/buster/slim
	url="https://github.com/debuerreotype/docker-debian-artifacts/raw/3ba08903ca3fd48fe59ba92b02744a2f5d4d9d6f/buster/slim/rootfs.tar.xz"
	;;

s390x)
	# https://github.com/debuerreotype/docker-debian-artifacts/tree/dist-s390x
	# https://github.com/debuerreotype/docker-debian-artifacts/tree/2fddbf8fe632fc5865b140341b68a1358586fff2/buster/slim
	url="https://github.com/debuerreotype/docker-debian-artifacts/raw/2fddbf8fe632fc5865b140341b68a1358586fff2/buster/slim/rootfs.tar.xz"
	;;

*)
	echo >&2 "error: unsupported debian architecture: $arch"
	exit 1
	;;
esac
rootfs="$TESTDATA/debian-${arch}.tar.xz"
get "$rootfs" "$url"
var=debian_image
echo "${var^^}=$rootfs"
