//go:build !linux

// +build: !linux

package file

import (
	"errors"
)

type MountInfo struct {
	Source string
	Target string
	FSType string
	Flags  uintptr
	Data   string
}

func Mount(mi MountInfo) error {
	if mi.Source == "" || mi.Target == "" || mi.FSType == "" {
		return errors.New("source、target or fstype is empty")
	}
	return errors.New("not support")
}

func Unmount(path string, flags int) error {
	return errors.New("not support")
}
