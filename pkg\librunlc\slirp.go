package librunlc

// from rootlesskit

import (
	"context"
	"errors"
	"fmt"
	"io"
	"io/fs"
	"net"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	"code.alipay.com/antjail/antjail/pkg/runtime"
	"code.alipay.com/antjail/antjail/pkg/util/file"
	fileUtil "code.alipay.com/antjail/antjail/pkg/util/file"
	"github.com/rootless-containers/rootlesskit/pkg/common"

	"github.com/sirupsen/logrus"
	"golang.org/x/sys/unix"

	"github.com/rootless-containers/rootlesskit/pkg/api"
	"github.com/rootless-containers/rootlesskit/pkg/network"
	"github.com/rootless-containers/rootlesskit/pkg/network/iputils"
)

type Features struct {
	// SupportsEnableIPv6 --enable-ipv6 (v0.2.0)
	SupportsEnableIPv6 bool
	// SupportsCIDR --cidr (v0.3.0)
	SupportsCIDR bool
	// SupportsDisableHostLoopback --disable-host-loopback (v0.3.0)
	SupportsDisableHostLoopback bool
	// SupportsAPISocket --api-socket (v0.3.0)
	SupportsAPISocket bool
	// SupportsEnableSandbox --enable-sandbox (v0.4.0)
	SupportsEnableSandbox bool
	// SupportsEnableSeccomp --enable-seccomp (v0.4.0)
	SupportsEnableSeccomp bool
	// KernelSupportsSeccomp whether the kernel supports slirp4netns --enable-seccomp
	KernelSupportsEnableSeccomp bool
	SupportsTCPPolicy           bool
	SupportsUDPPolicy           bool
	SupportsDomainPolicy        bool

	SupportsEnableDebug bool
}

func DetectFeatures() (*Features, error) {
	realBinary, err := GetSlirp4netns()
	if err != nil {
		return nil, err
	}
	if _, err := os.Stat(realBinary); os.IsNotExist(err) {
		return nil, fmt.Errorf("not found %s in $PATH and /etc/antjail/bin/", err.Error())
	}
	cmd := exec.Command(realBinary, "--help")
	cmd.Env = os.Environ()
	b, err := cmd.CombinedOutput()
	s := string(b)
	if err != nil {
		return nil, fmt.Errorf(
			"command \"%s --help\" failed, make sure slirp4netns v0.4.0+ is installed: %q: %w",
			realBinary, s, err,
		)
	}
	if !strings.Contains(s, "--netns-type") {
		// We don't use --netns-type, but we check the presence of --netns-type to
		// ensure slirp4netns >= v0.4.0: https://github.com/rootless-containers/rootlesskit/issues/143
		return nil, errors.New("slirp4netns seems older than v0.4.0")
	}
	kernelSupportsEnableSeccomp := false
	if unix.Prctl(unix.PR_GET_SECCOMP, 0, 0, 0, 0) != unix.EINVAL {
		kernelSupportsEnableSeccomp = unix.Prctl(unix.PR_SET_SECCOMP, unix.SECCOMP_MODE_FILTER, 0, 0, 0) != unix.EINVAL
	}
	f := Features{
		SupportsEnableIPv6:          strings.Contains(s, "--enable-ipv6"),
		SupportsCIDR:                strings.Contains(s, "--cidr"),
		SupportsDisableHostLoopback: strings.Contains(s, "--disable-host-loopback"),
		SupportsAPISocket:           strings.Contains(s, "--api-socket"),
		SupportsEnableSandbox:       strings.Contains(s, "--enable-sandbox"),
		SupportsEnableSeccomp:       strings.Contains(s, "--enable-seccomp"),
		SupportsTCPPolicy:           strings.Contains(s, "--tcp-policy"),
		SupportsUDPPolicy:           strings.Contains(s, "--udp-policy"),
		SupportsDomainPolicy:        strings.Contains(s, "--domain-policy"),
		KernelSupportsEnableSeccomp: kernelSupportsEnableSeccomp,
		SupportsEnableDebug:         strings.Contains(s, "--enable-debug"),
	}
	return &f, nil
}

// NewParentDriver instantiates new parent driver.
// Requires slirp4netns v0.4.0 or later.
func NewParentDriver(logWriter io.Writer, mtu int, ipnet *net.IPNet, ifname string, disableHostLoopback bool, apiSocketPath string,
	enableSandbox, enableSeccomp, enableIPv6 bool, tcpPolicy string, udpPolicy string, domainPolicy string, enableDebug bool) (*parentDriver, error) {
	if mtu < 0 {
		return nil, errors.New("got negative mtu")
	}
	if mtu == 0 {
		mtu = 65520
	}

	if ifname == "" {
		ifname = "tap0"
	}

	features, err := DetectFeatures()
	if err != nil {
		return nil, err
	}
	if enableIPv6 && !features.SupportsEnableIPv6 {
		return nil, errors.New("this version of slirp4netns does not support --enable-ipv6")
	}
	if ipnet != nil && !features.SupportsCIDR {
		return nil, errors.New("this version of slirp4netns does not support --cidr")
	}
	if disableHostLoopback && !features.SupportsDisableHostLoopback {
		return nil, errors.New("this version of slirp4netns does not support --disable-host-loopback")
	}
	if apiSocketPath != "" && !features.SupportsAPISocket {
		return nil, errors.New("this version of slirp4netns does not support --api-socket")
	}
	if enableSandbox && !features.SupportsEnableSandbox {
		return nil, errors.New("this version of slirp4netns does not support --enable-sandbox")
	}
	if enableSeccomp && !features.SupportsEnableSeccomp {
		return nil, errors.New("this version of slirp4netns does not support --enable-seccomp")
	}
	if enableSeccomp && !features.KernelSupportsEnableSeccomp {
		return nil, errors.New("kernel does not support seccomp")
	}
	if tcpPolicy != "" && !features.SupportsTCPPolicy {
		return nil, errors.New("this version of slirp4netns does not support --tcp-policy")
	}
	if udpPolicy != "" && !features.SupportsUDPPolicy {
		return nil, errors.New("this version of slirp4netns does not support --udp-policy")
	}
	if domainPolicy != "" && !features.SupportsDomainPolicy {
		return nil, errors.New("this version of slirp4netns does not support --domain-policy")
	}
	if enableDebug && !features.SupportsEnableDebug {
		return nil, errors.New("this version of slirp4netns does not support --enable-debug")
	}

	realBinary, err := GetSlirp4netns()
	if err != nil {
		return nil, err
	}

	return &parentDriver{
		logWriter:           logWriter,
		binary:              realBinary,
		mtu:                 mtu,
		ipnet:               ipnet,
		disableHostLoopback: disableHostLoopback,
		apiSocketPath:       apiSocketPath,
		enableSandbox:       enableSandbox,
		enableSeccomp:       enableSeccomp,
		enableIPv6:          enableIPv6,
		ifname:              ifname,
		tcpPolicy:           tcpPolicy,
		udpPolicy:           udpPolicy,
		domainPolicy:        domainPolicy,
		enableDebug:         enableDebug,
	}, nil
}

type parentDriver struct {
	logWriter           io.Writer
	binary              string
	mtu                 int
	ipnet               *net.IPNet
	disableHostLoopback bool
	apiSocketPath       string
	enableSandbox       bool
	enableSeccomp       bool
	enableIPv6          bool
	ifname              string
	infoMu              sync.RWMutex
	info                func() *api.NetworkDriverInfo
	tcpPolicy           string
	udpPolicy           string
	domainPolicy        string
	enableDebug         bool
}

const DriverName = "antslirp4netns"

func (d *parentDriver) Info(ctx context.Context) (*api.NetworkDriverInfo, error) {
	d.infoMu.RLock()
	infoFn := d.info
	d.infoMu.RUnlock()
	if infoFn == nil {
		return &api.NetworkDriverInfo{
			Driver: DriverName,
		}, nil
	}

	return infoFn(), nil
}

func (d *parentDriver) MTU() int {
	return d.mtu
}

type Option struct {
	TCPPolicy    string
	DomainPolicy string
}

func (d *parentDriver) ConfigureNetwork(tid int, stateDir string) (*common.NetworkMessage, func() error, error) {
	tap := d.ifname
	var cleanups []func() error
	//if err := PrepareTap(tap); err != nil {
	//return nil, common.Seq(cleanups), fmt.Errorf("setting up tap %s: %w", tap, err)
	//}
	readyR, readyW, err := os.Pipe()
	if err != nil {
		return nil, common.Seq(cleanups), err
	}
	defer readyR.Close()
	defer readyW.Close()
	// -r: readyFD (requires slirp4netns >= v0.4.0: https://github.com/rootless-containers/rootlesskit/issues/143)
	opts := []string{"--mtu", strconv.Itoa(d.mtu), "-r", "3", "--configure"}
	if d.disableHostLoopback {
		opts = append(opts, "--disable-host-loopback")
	}
	if d.ipnet != nil {
		opts = append(opts, "--cidr", d.ipnet.String())
	}
	if d.apiSocketPath != "" {
		opts = append(opts, "--api-socket", d.apiSocketPath)
	}
	if d.enableSandbox {
		opts = append(opts, "--enable-sandbox")
	}
	if d.enableSeccomp {
		opts = append(opts, "--enable-seccomp")
	}
	if d.enableIPv6 {
		opts = append(opts, "--enable-ipv6")
	}
	if d.tcpPolicy != "" {
		opts = append(opts, "--tcp-policy", d.tcpPolicy)
	}
	if d.udpPolicy != "" {
		opts = append(opts, "--udp-policy", d.udpPolicy)
	}
	if d.domainPolicy != "" {
		opts = append(opts, "--domain-policy", d.domainPolicy)
	}
	if d.enableDebug {
		opts = append(opts, "--enable-debug")
	}

	opts = append(opts, "--netns-type=pid")
	cmd := exec.Command(d.binary, append(opts, []string{strconv.Itoa(tid), tap}...)...)
	// FIXME: Stdout doen't seem captured
	cmd.Stdout = d.logWriter
	cmd.Stderr = d.logWriter
	cmd.SysProcAttr = &syscall.SysProcAttr{
		Pdeathsig: syscall.SIGKILL,
	}
	cmd.ExtraFiles = append(cmd.ExtraFiles, readyW)
	cleanups = append(cleanups, func() error {
		logrus.Debugf("killing slirp4netns")
		if cmd.Process != nil {
			_ = cmd.Process.Kill()
		}
		wErr := cmd.Wait()
		logrus.Debugf("killed slirp4netns: %v", wErr)
		return nil
	})
	if err := cmd.Start(); err != nil {
		return nil, common.Seq(cleanups), fmt.Errorf("executing %v: %w", cmd, err)
	}

	if err := waitForReadyFD(cmd.Process.Pid, readyR); err != nil {
		return nil, common.Seq(cleanups), fmt.Errorf("waiting for ready fd (%v): %w", cmd, err)
	}
	netmsg := common.NetworkMessage{
		Dev: tap,
		MTU: d.mtu,
	}
	if d.ipnet != nil {
		// TODO: get the actual configuration via slirp4netns API?
		x, err := iputils.AddIPInt(d.ipnet.IP, 100)
		if err != nil {
			return nil, common.Seq(cleanups), err
		}
		netmsg.IP = x.String()
		netmsg.Netmask, _ = d.ipnet.Mask.Size()
		x, err = iputils.AddIPInt(d.ipnet.IP, 2)
		if err != nil {
			return nil, common.Seq(cleanups), err
		}
		netmsg.Gateway = x.String()
		x, err = iputils.AddIPInt(d.ipnet.IP, 3)
		if err != nil {
			return nil, common.Seq(cleanups), err
		}
		netmsg.DNS = x.String()
	} else {
		netmsg.IP = "**********"
		netmsg.Netmask = 24
		netmsg.Gateway = "********"
		netmsg.DNS = "********"
	}

	d.infoMu.Lock()
	d.info = func() *api.NetworkDriverInfo {
		return &api.NetworkDriverInfo{
			Driver:         DriverName,
			DNS:            []net.IP{net.ParseIP(netmsg.DNS)},
			ChildIP:        net.ParseIP(netmsg.IP),
			DynamicChildIP: false,
		}
	}
	d.infoMu.Unlock()
	return &netmsg, common.Seq(cleanups), nil
}

// waitForReady is from libpod
// https://github.com/containers/libpod/blob/e6b843312b93ddaf99d0ef94a7e60ff66bc0eac8/libpod/networking_linux.go#L272-L308
func waitForReadyFD(cmdPid int, r *os.File) error {
	b := make([]byte, 16)
	for {
		if err := r.SetDeadline(time.Now().Add(3 * time.Second)); err != nil {
			return fmt.Errorf("error setting slirp4netns pipe timeout: %w", err)
		}
		if _, err := r.Read(b); err == nil {
			break
		} else {
			if os.IsTimeout(err) {
				// Check if the process is still running.
				var status syscall.WaitStatus
				pid, err := syscall.Wait4(cmdPid, &status, syscall.WNOHANG, nil)
				if err != nil {
					return fmt.Errorf("failed to read slirp4netns process status: %w", err)
				}
				if pid != cmdPid {
					continue
				}
				if status.Exited() {
					return errors.New("slirp4netns failed")
				}
				if status.Signaled() {
					return errors.New("slirp4netns killed by signal")
				}
				continue
			}
			return fmt.Errorf("failed to read from slirp4netns sync pipe: %w", err)
		}
	}
	return nil
}

func NewChildDriver() network.ChildDriver {
	return &childDriver{}
}

type childDriver struct {
}

func (d *childDriver) ConfigureNetworkChild(netmsg *common.NetworkMessage) (string, error) {
	tap := netmsg.Dev
	if tap == "" {
		return "", errors.New("could not determine the preconfigured tap")
	}
	// tap is created and "up".
	// IP stuff and MTU are not configured by the parent here,
	// and they are up to the child.
	return tap, nil
}

func PrepareTap(tap string) error {
	cmds := [][]string{
		[]string{"ip", "tuntap", "add", "name", tap, "mode", "tap"},
		[]string{"ip", "link", "set", tap, "up"},
	}
	if err := common.Execs(os.Stderr, os.Environ(), cmds); err != nil {
		return fmt.Errorf("executing %v: %w", cmds, err)
	}
	return nil
}

func GetSlirp4netns() (string, error) {
	realBinary, err := exec.LookPath(DriverName)
	if err != nil {
		if execErr, ok := err.(*exec.Error); ok {
			if execErr.Err != exec.ErrNotFound {
				return "", fmt.Errorf("slirp4netns binary %q is not installed: %w", DriverName, err)
			} else {
				realBinary = filepath.Join("/etc/antjail/bin/", DriverName)
			}
		} else {
			return "", fmt.Errorf("slirp4netns binary %q is not installed: %w", DriverName, err)
		}
	}
	return realBinary, nil
}

func prepareTUNCharDevice(config *runtime.Config) error {
	_, err := os.Stat("/dev/net/tun")
	if err != nil && !os.IsNotExist(err) {
		return err
	}
	if err == nil {
		return nil
	}

	tDevPath := "/tmp/antjail_dev_mount_point"

	if _, err := os.Stat(tDevPath); err != nil && !os.IsNotExist(err) {
		return err
	} else if os.IsNotExist(err) {
		if err := os.MkdirAll(tDevPath, fs.ModePerm); err != nil {
			return fmt.Errorf("mkdir /tmp/antjail_dev error: %s", err.Error())
		}

		mountInfo := file.MountInfo{
			Source: "dev",
			Target: tDevPath,
			FSType: "devtmpfs",
		}
		//mount devtempfs
		if err := fileUtil.Mount(mountInfo); err != nil {
			return fmt.Errorf("mount devtmpfs error: %s", err.Error())
		}
	}

	tunpath := tDevPath + "/net"
	if err := os.Symlink(tunpath, "/dev/net"); err != nil {
		return fmt.Errorf("link %s to dev error: %s", tDevPath, err.Error())
	}

	config.OCIConfig.Linux.MaskedPaths = append(config.OCIConfig.Linux.MaskedPaths, tDevPath)
	return nil
}
