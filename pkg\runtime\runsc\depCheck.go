package runsc

import (
	"errors"
	"fmt"
	"strconv"
	"strings"

	kernalUtil "code.alipay.com/antjail/antjail/pkg/util/kernal"

	"k8s.io/klog/v2"
)

const (
	MajorLimit = 4
	MinorLimit = 9
)

// DepCheck ...
func (*runsc) depCheck() error {
	kInfo, err := kernalUtil.GetKernalInfo()
	if err != nil {
		return errors.New("only support linux")
	}
	klog.V(4).Infof("%#v", kInfo)
	// 将内核版本号按照"."分割成数组
	release := strings.Split(kInfo.Release, ".")
	if len(release) < 2 {
		return errors.New("get kernal version fatal")
	}
	major, _ := strconv.Atoi(release[0])
	minor, _ := strconv.Atoi(release[1])
	if major < MajorLimit {
		return fmt.Errorf("antjail's minimal supported kernel version is 4.9,but this machine kernel version is %s", kInfo.Release)
	}
	if major == MajorLimit && minor < MinorLimit {
		return fmt.Errorf("antjail's minimal supported kernel version is 4.9,but this machine kernel version is %s", kInfo.Release)
	}
	return nil
}
