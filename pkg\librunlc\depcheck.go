package librunlc

import (
	"strconv"
	"strings"

	"code.alipay.com/antjail/antjail/pkg/util/kernal"
	"k8s.io/klog/v2"
)

const (
	MajorLimit = 4
)

func pidNamespeceCheck() bool {
	kInfo, err := kernal.GetKernalInfo()
	if err != nil {
		return false
	}
	klog.V(4).Infof("%#v", kInfo)
	// 将内核版本号按照"."分割成数组
	release := strings.Split(kInfo.Release, ".")
	if len(release) < 2 {
		return false
	}
	major, _ := strconv.Atoi(release[0])
	if major < MajorLimit {
		return false
	}
	return true
}
