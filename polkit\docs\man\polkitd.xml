<?xml version="1.0"?>
<!DOCTYPE book PUBLIC "-//OASIS//DTD DocBook XML V4.1.2//EN"
               "http://www.oasis-open.org/docbook/xml/4.1.2/docbookx.dtd" [
<!ENTITY version SYSTEM "../version.xml">
]>
<refentry id="polkitd.8">
  <refentryinfo>
    <title>polkitd</title>
    <date>May 2009</date>
    <productname>polkit</productname>
  </refentryinfo>

  <refmeta>
    <refentrytitle>polkitd</refentrytitle>
    <manvolnum>8</manvolnum>
    <refmiscinfo class="version"></refmiscinfo>
  </refmeta>

  <refnamediv>
    <refname>polkitd</refname>
    <refpurpose>The polkit system daemon</refpurpose>
  </refnamediv>

  <refsynopsisdiv>
    <cmdsynopsis>
      <command>polkitd</command>
    </cmdsynopsis>
  </refsynopsisdiv>

  <refsect1 id="polkitd-description"><title>DESCRIPTION</title>
    <para>
      <command>polkitd</command> provides
      the <emphasis>org.freedesktop.PolicyKit1</emphasis> D-Bus
      service on the system message bus. Users or administrators
      should never need to start this daemon as it will be
      automatically started by
      <citerefentry><refentrytitle>dbus-daemon</refentrytitle><manvolnum>1</manvolnum></citerefentry>
      or
      <citerefentry><refentrytitle>systemd</refentrytitle><manvolnum>1</manvolnum></citerefentry>
      whenever an application calls into the service.
    </para>

    <para>
      <command>polkitd</command> must be started with superuser
      privileges but drops privileges early by switching to the
      unprivileged <emphasis>polkitd</emphasis> system user.
    </para>

    <para>
      See the <link
      linkend="polkit.8"><citerefentry><refentrytitle>polkit</refentrytitle><manvolnum>8</manvolnum></citerefentry></link>
      man page for more information.

    </para>
  </refsect1>

  <refsect1 id="polkitd-author"><title>AUTHOR</title>
    <para>
      Written by David Zeuthen <email><EMAIL></email> with
      a lot of help from many others.
    </para>
  </refsect1>

  <refsect1 id="polkitd-bugs">
    <title>BUGS</title>
    <para>
      Please send bug reports to either the distribution or the
      polkit-devel mailing list,
      see the link <ulink url="http://lists.freedesktop.org/mailman/listinfo/polkit-devel"/>
      on how to subscribe.
    </para>
  </refsect1>

  <refsect1 id="polkitd-see-also">
    <title>SEE ALSO</title>
    <para>
      <link linkend="polkit.8"><citerefentry><refentrytitle>polkit</refentrytitle><manvolnum>8</manvolnum></citerefentry></link>,
      <link linkend="pkaction.1"><citerefentry><refentrytitle>pkaction</refentrytitle><manvolnum>1</manvolnum></citerefentry></link>,
      <link linkend="pkcheck.1"><citerefentry><refentrytitle>pkcheck</refentrytitle><manvolnum>1</manvolnum></citerefentry></link>,
      <link linkend="pkexec.1"><citerefentry><refentrytitle>pkexec</refentrytitle><manvolnum>1</manvolnum></citerefentry></link>,
      <link linkend="pkttyagent.1"><citerefentry><refentrytitle>pkttyagent</refentrytitle><manvolnum>1</manvolnum></citerefentry></link>,
      <citerefentry><refentrytitle>dbus-daemon</refentrytitle><manvolnum>1</manvolnum></citerefentry>,
      <citerefentry><refentrytitle>systemd</refentrytitle><manvolnum>1</manvolnum></citerefentry>
    </para>
  </refsect1>
</refentry>
