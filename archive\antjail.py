#!/usr/bin/python3
import sys
import getopt
import json
import subprocess
import uuid
import os
import shutil
import asyncio


srootdir = "/var/run/antjail/"
cgroup_root = "/sys/fs/cgroup/"
sandbox_name = ""
runsc_flags = []
configj = None
oci_configj = {}

async def _read_stream(stream, cb):
    while True:
        line = await stream.readline()
        if line:
            cb(line)
        else:
            break

async def _stream_subprocess(cmd, stdout_cb, stderr_cb):
    process = await asyncio.create_subprocess_exec(*cmd,
            stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE)

    await asyncio.wait([
        _read_stream(process.stdout, stdout_cb),
        _read_stream(process.stderr, stderr_cb)
    ])
    return await process.wait()


def execute(cmd, stdout_cb, stderr_cb):
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
   # loop = asyncio.get_event_loop()
    rc = loop.run_until_complete(
        _stream_subprocess(
            cmd,
            stdout_cb,
            stderr_cb,
    ))
    loop.close()
    return rc


def get_configj(conffile):
    global configj
    with open(conffile, 'r') as j:
        root = json.loads(j.read())
        configj = root

def apply_file(wpaths):
    global oci_configj
    for path in wpaths:
        item = {"destination": path,
                "type": "bind",
                "source": path,
                "options": [
                    "rbind",
                    "rw"
                    ]
                }
        oci_configj["mounts"].append(item)

def gen_netpolicy_conf(name, policy):
    with open(name, 'w') as j:
        json.dump(policy, j)

def apply_security():
    global configj
    global oci_configj
    global runsc_flags

    if not configj:
        return
    if not configj.__contains__("security"):
        return
    if configj["security"].__contains__("file"):
        wpaths = configj["security"]["file"]["writablePaths"]
        apply_file(wpaths)
    if configj["security"].__contains__("network"):
        mode = configj["security"]["network"]["mode"]
        if mode == "none":
            #runsc_flags.append("--TESTONLY-unsafe-nonroot")
            return
        if mode == "host":
            runsc_flags.append("--network host")
        if mode == "hostwithpolicy":
            runsc_flags.append("--network host")
            gen_netpolicy_conf(srootdir + sandbox_name + "/netacl.json", configj["security"]["network"]["policy"])
            runsc_flags.append("--net-acl " + srootdir + sandbox_name + "/netacl.json")
    namespaces = oci_configj["linux"]["namespaces"]
    namespaces = [item for item  in namespaces if item["type"] != "network"]
    oci_configj["linux"]["namespaces"] = namespaces

def apply_cmdline(args):
    global oci_configj
    oci_configj["process"]["args"] = args
    oci_configj["process"]["cwd"] = os.getcwd()

def start_process(name, runsc_flags):
    #print(runsc_flags)
    os.chdir(srootdir+name)
    flags = ' '.join(runsc_flags)
    cmdline = ["/etc/antjail/bin/runsc"]
    if flags != "":
        cmdline.extend(flags.split(' '))
   # print(cmdline)
    cmdline.append("run")
    cmdline.append(name)
    try: 
        rc = execute(
          cmdline,
          lambda x: print(x.decode("utf-8"),end=''),
          lambda x: print(x.decode("utf-8"),end='' ))
    except Exception as e:
         print(e)
         cleanup_kvm()
         delete_rundir(sandbox_name)
         delete_cgroup()
    sys.exit(rc)
 
def create_rundir(sandbox_name):
    if not os.path.exists("/var/run/antjail/"):
        os.makedirs("/var/run/antjail/")
    if os.path.exists(srootdir+sandbox_name):
        shutil.rmtree(srootdir+sandbox_name)
    os.makedirs(srootdir+sandbox_name)
    return True

def delete_rundir(sandbox_name):
    if os.path.exists(srootdir+sandbox_name):
        shutil.rmtree(srootdir+sandbox_name)

def create_ociconfigj():
    global oci_configj
    with open("/etc/antjail/config_templ.json", 'r') as j:
        oci_configj = json.loads(j.read())

def apply_meta():
    global runsc_flags
    global configj
    if not configj:
        return
    else:
        if configj.__contains__("meta") and configj["meta"].__contains__("arg"):
            if configj["meta"]["arg"] != "":
                runsc_flags.append(configj["meta"]["arg"])


def cleanup_kvm():
    if os.path.exists("/tmp/antjail_dev"):
        os.system("umount /tmp/antjail_dev")
        shutil.rmtree("/tmp/antjail_dev")
    if os.path.islink("/dev/kvm"):
        os.unlink("/dev/kvm")

def apply_system():
    global oci_configj
    global configj
    global runsc_flags
    if not configj:
        return
    if not configj.__contains__("system"):
        return
    if configj["system"].__contains__("root"):
        oci_configj["root"]["path"] = configj["system"]["root"]["path"]
        oci_configj["root"]["readonly"] = configj["system"]["root"]["readonly"]
    if configj["system"].__contains__("accel"):
        accel = configj["system"]["accel"]
        if accel == "kvm":
            if os.path.exists("/dev/kvm") and (not os.path.islink("/dev/kvm")):
                    runsc_flags.append("--platform=kvm")
            else:
                try:
                    if not os.path.exists("/tmp/antjail_dev"):
                        os.makedirs("/tmp/antjail_dev")
                        os.system("mount -t devtmpfs dev /tmp/antjail_dev/")
                    if not os.path.exists("/tmp/antjail_dev/kvm"):
                        print("not find kvm")
                    else:
                        os.system("ln -s /tmp/antjail_dev/kvm /dev/kvm")
                        os.system("echo a > /sys/fs/cgroup/devices/devices.allow")
                        runsc_flags.append("--platform=kvm")
                except Exception as e:
                    print(e)
                    print("try use kvm error, use ptrace instaed")
    if configj["system"].__contains__("cpu"):
        cpu = {}
        oci_configj["linux"]["resources"]["cpu"]["quota"] = int(100000 * configj["system"]["cpu"]["number"])

    if configj["system"].__contains__("memory"):
        oci_configj["linux"]["resources"]["memory"]["limit"] = configj["system"]["memory"]["max"]

def commit_oci_configj():
    global srootdir
    global sandbox_name
    global oci_configj
    with open(srootdir + sandbox_name + "/config.json", 'w') as j:
        json.dump(oci_configj, j)


def delete_cgroup():
    global sandbox_name
    global cgroup_root
    if os.path.exists(cgroup_root+"cpu/"+sandbox_name):
        os.rmdir(cgroup_root+"cpu/"+sandbox_name)
    if os.path.exists(cgroup_root+"memory/" + sandbox_name):
        os.rmdir(cgroup_root+"memory/"+sandbox_name)

def main(argv):
    optlist, args = getopt.getopt(argv[1:], 'c:')
    if len(args) == 0:
        print("please provide the process you want to run!\n")
        sys.exit(-1)
    #apply_args(args)
    #runsc_flags = []
    global sandbox_name
    global configj
    global runsc_flags
    if len(optlist) != 0:
        if len(optlist) != 1:
            print("invalide options\n")
            sys.exit(-1)
        configname = optlist[0][1]

        if not configname.startswith("/"):
            configname = os.getcwd()+"/"+configname
        get_configj(configname)
        if configj.__contains__("meta"):
            if configj["meta"].__contains__("name"):
                sandbox_name = configj["meta"]["name"]

    if sandbox_name == "":
        sandbox_name = str(uuid.uuid4())

    create_rundir(sandbox_name)

    create_ociconfigj()
    delete_cgroup()

    apply_meta()
    apply_system()
    apply_security()
    apply_cmdline(args)
    commit_oci_configj()
    start_process(sandbox_name, runsc_flags)
    cleanup_kvm()
    delete_rundir(sandbox_name)

if __name__ == '__main__':
    main(sys.argv)
